import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'local_storage_service.g.dart';

class LocalStorageService {
  // Secure storage instance
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Updated keys for better organization
  static const String _planPreferencesKey = 'plan_preferences'; // Renamed from onboarding_data
  static const String _notificationSettingsKey = 'notification_settings'; // New separate key
  static const String _onboardingCompletedKey = 'onboarding_completed';
  static const String _userPreferencesKey = 'user_preferences'; // Keep for backward compatibility
  static const String _userProfileKey = 'user_profile'; // User profile data (moved from secure storage)
  static const String _mealPlansKey = 'meal_plans';
  static const String _mealConsumptionPrefix = 'meal_consumption_';
  static const String _waterIntakePrefix = 'water_intake_';
  static const String _waterSettingsKey = 'water_settings';
  static const String _shoppingListPrefix = 'shopping_list_'; // Legacy key prefix for migration
  static const String _shoppingListsKey = 'shopping_lists'; // New consolidated key for complete shopping lists
  static const String _generatingShoppingListKey = 'generating_shopping_list'; // Background generation flag
  static const String _weightTrackingKey = 'weight_tracking_data';
  static const String _weightSettingsKey = 'weight_settings';
  static const String _notificationsPrefix = 'notifications_';
  static const String _notificationStatsKey = 'notification_stats';
  static const String _homeLayoutModeKey = 'home_layout_mode';
  static const String _mealGenerationLoadingKey = 'meal_generation_loading';
  static const String _mealGenerationErrorKey = 'meal_generation_error';

  // Legacy key for migration
  static const String _legacyOnboardingDataKey = 'onboarding_data';

  final SharedPreferences _prefs;

  LocalStorageService(this._prefs);

  /// Save plan preferences to local storage (renamed from onboarding data)
  Future<bool> savePlanPreferences(Map<String, dynamic> data) async {
    try {
      final jsonString = jsonEncode(data);
      return await _prefs.setString(_planPreferencesKey, jsonString);
    } catch (e) {
      return false;
    }
  }

  /// Load plan preferences from local storage (with migration from legacy key)
  Map<String, dynamic>? loadPlanPreferences() {
    try {
      // Try new key first
      String? jsonString = _prefs.getString(_planPreferencesKey);

      // If not found, try legacy key for migration
      if (jsonString == null) {
        jsonString = _prefs.getString(_legacyOnboardingDataKey);
        if (jsonString != null) {
          // Migrate data to new key
          _prefs.setString(_planPreferencesKey, jsonString);
          _prefs.remove(_legacyOnboardingDataKey);
        }
      }

      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Clear plan preferences from local storage
  Future<bool> clearPlanPreferences() async {
    try {
      await _prefs.remove(_planPreferencesKey);
      await _prefs.remove(_legacyOnboardingDataKey); // Also clear legacy key
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Legacy method for backward compatibility - delegates to new methods
  @Deprecated('Use savePlanPreferences instead')
  Future<bool> saveOnboardingData(Map<String, dynamic> data) async {
    return await savePlanPreferences(data);
  }

  /// Legacy method for backward compatibility - delegates to new methods
  @Deprecated('Use loadPlanPreferences instead')
  Map<String, dynamic>? loadOnboardingData() {
    return loadPlanPreferences();
  }

  /// Legacy method for backward compatibility - delegates to new methods
  @Deprecated('Use clearPlanPreferences instead')
  Future<bool> clearOnboardingData() async {
    return await clearPlanPreferences();
  }

  /// Mark onboarding as completed
  Future<bool> setOnboardingCompleted(bool completed) async {
    try {
      return await _prefs.setBool(_onboardingCompletedKey, completed);
    } catch (e) {
      return false;
    }
  }

  /// Check if onboarding is completed
  bool isOnboardingCompleted() {
    return _prefs.getBool(_onboardingCompletedKey) ?? false;
  }

  /// Save current onboarding step
  Future<bool> saveCurrentOnboardingStep(int step) async {
    try {
      return await _prefs.setInt('current_onboarding_step', step);
    } catch (e) {
      return false;
    }
  }

  /// Load current onboarding step
  int? loadCurrentOnboardingStep() {
    try {
      return _prefs.getInt('current_onboarding_step');
    } catch (e) {
      return null;
    }
  }

  /// Clear current onboarding step
  Future<bool> clearCurrentOnboardingStep() async {
    try {
      await _prefs.remove('current_onboarding_step');
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Clear ALL data from SharedPreferences and secure storage (for logout)
  /// Performs a complete storage clear to ensure no Flutter-prefixed keys or any other keys remain
  /// Preserves only the has_seen_intro_wizard flag to avoid showing intro wizard again
  Future<bool> clearUserData() async {
    try {
      // Preserve has_seen_intro_wizard flag before clearing
      final hasSeenIntroWizard = _prefs.getBool('has_seen_intro_wizard');

      // Clear ALL keys from SharedPreferences storage
      await _prefs.clear();

      // Clear all secure storage data
      await clearSecure();

      // Restore has_seen_intro_wizard flag if it was set (regardless of true/false)
      if (hasSeenIntroWizard != null) {
        await _prefs.setBool('has_seen_intro_wizard', hasSeenIntroWizard);
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Save notification settings to local storage
  Future<bool> saveNotificationSettings(Map<String, dynamic> settings) async {
    try {
      final jsonString = jsonEncode(settings);
      return await _prefs.setString(_notificationSettingsKey, jsonString);
    } catch (e) {
      return false;
    }
  }

  /// Load notification settings from local storage
  Map<String, dynamic>? loadNotificationSettings() {
    try {
      final jsonString = _prefs.getString(_notificationSettingsKey);
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Clear notification settings from local storage
  Future<bool> clearNotificationSettings() async {
    try {
      await _prefs.remove(_notificationSettingsKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Save user preferences
  Future<bool> saveUserPreferences(Map<String, dynamic> preferences) async {
    try {
      final jsonString = jsonEncode(preferences);
      return await _prefs.setString(_userPreferencesKey, jsonString);
    } catch (e) {
      return false;
    }
  }

  /// Load user preferences
  Map<String, dynamic>? loadUserPreferences() {
    try {
      final jsonString = _prefs.getString(_userPreferencesKey);
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Clear user preferences
  Future<bool> clearUserPreferences() async {
    try {
      await _prefs.remove(_userPreferencesKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Save user profile to local storage
  Future<bool> saveUserProfile(Map<String, dynamic> profile) async {
    try {
      final jsonString = jsonEncode(profile);
      return await _prefs.setString(_userProfileKey, jsonString);
    } catch (e) {
      return false;
    }
  }

  /// Load user profile from local storage
  Map<String, dynamic>? loadUserProfile() {
    try {
      final jsonString = _prefs.getString(_userProfileKey);
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Clear user profile from local storage
  Future<bool> clearUserProfile() async {
    try {
      await _prefs.remove(_userProfileKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Save current meal plan to local storage with embedded consumption data
  Future<bool> saveCurrentMealPlan(Map<String, dynamic> mealPlan) async {
    try {
      final jsonString = jsonEncode(mealPlan);
      return await _prefs.setString(_mealPlansKey, jsonString);
    } catch (e) {
      return false;
    }
  }

  /// Load current meal plan from local storage and migrate consumption data if needed
  Map<String, dynamic>? loadCurrentMealPlan() {
    try {
      final jsonString = _prefs.getString(_mealPlansKey);
      if (jsonString != null) {
        final mealPlanData = jsonDecode(jsonString) as Map<String, dynamic>;

        // Check if this meal plan needs consumption data migration
        final migratedData = _migrateConsumptionDataToMealPlan(mealPlanData);

        // If migration occurred, save the updated data and clean up old consumption data
        if (migratedData != mealPlanData) {
          saveCurrentMealPlan(migratedData);
          _cleanupOldConsumptionData(mealPlanData);
        }

        return migratedData;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Clear current meal plan from local storage
  Future<bool> clearCurrentMealPlan() async {
    try {
      await _prefs.remove(_mealPlansKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Save a string value
  Future<bool> saveString(String key, String value) async {
    try {
      return await _prefs.setString(key, value);
    } catch (e) {
      return false;
    }
  }

  /// Load a string value
  String? loadString(String key) {
    try {
      return _prefs.getString(key);
    } catch (e) {
      return null;
    }
  }

  /// Save an integer value
  Future<bool> saveInt(String key, int value) async {
    try {
      return await _prefs.setInt(key, value);
    } catch (e) {
      return false;
    }
  }

  /// Load an integer value
  int? loadInt(String key) {
    try {
      return _prefs.getInt(key);
    } catch (e) {
      return null;
    }
  }

  /// Save a boolean value
  Future<bool> saveBool(String key, bool value) async {
    try {
      return await _prefs.setBool(key, value);
    } catch (e) {
      return false;
    }
  }

  /// Load a boolean value
  bool? loadBool(String key) {
    try {
      return _prefs.getBool(key);
    } catch (e) {
      return null;
    }
  }

  /// Save a double value
  Future<bool> saveDouble(String key, double value) async {
    try {
      return await _prefs.setDouble(key, value);
    } catch (e) {
      return false;
    }
  }

  /// Load a double value
  double? loadDouble(String key) {
    try {
      return _prefs.getDouble(key);
    } catch (e) {
      return null;
    }
  }

  /// Save a list of strings
  Future<bool> saveStringList(String key, List<String> value) async {
    try {
      return await _prefs.setStringList(key, value);
    } catch (e) {
      return false;
    }
  }

  /// Load a list of strings
  List<String>? loadStringList(String key) {
    try {
      return _prefs.getStringList(key);
    } catch (e) {
      return null;
    }
  }

  /// Remove a specific key
  Future<bool> remove(String key) async {
    try {
      return await _prefs.remove(key);
    } catch (e) {
      return false;
    }
  }

  /// Clear all data
  Future<bool> clearAll() async {
    try {
      return await _prefs.clear();
    } catch (e) {
      return false;
    }
  }

  /// Check if a key exists
  bool containsKey(String key) {
    return _prefs.containsKey(key);
  }

  /// Get all keys
  Set<String> getAllKeys() {
    return _prefs.getKeys();
  }

  /// Save meal consumption status
  Future<bool> saveMealConsumptionStatus(String uniqueKey, Map<String, dynamic> status) async {
    try {
      final jsonString = jsonEncode(status);
      return await _prefs.setString('$_mealConsumptionPrefix$uniqueKey', jsonString);
    } catch (e) {
      return false;
    }
  }

  /// Load meal consumption status
  Map<String, dynamic>? loadMealConsumptionStatus(String uniqueKey) {
    try {
      final jsonString = _prefs.getString('$_mealConsumptionPrefix$uniqueKey');
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Load all meal consumption statuses for a meal plan
  Map<String, Map<String, dynamic>> loadAllMealConsumptionStatuses(String mealPlanId) {
    try {
      final Map<String, Map<String, dynamic>> statuses = {};
      final allKeys = _prefs.getKeys();

      for (final key in allKeys) {
        if (key.startsWith('$_mealConsumptionPrefix${mealPlanId}_')) {
          final jsonString = _prefs.getString(key);
          if (jsonString != null) {
            final statusData = jsonDecode(jsonString) as Map<String, dynamic>;
            final uniqueKey = key.replaceFirst(_mealConsumptionPrefix, '');
            statuses[uniqueKey] = statusData;
          }
        }
      }

      return statuses;
    } catch (e) {
      return {};
    }
  }

  /// Remove meal consumption status
  Future<bool> removeMealConsumptionStatus(String uniqueKey) async {
    try {
      return await _prefs.remove('$_mealConsumptionPrefix$uniqueKey');
    } catch (e) {
      return false;
    }
  }

  /// Clear all meal consumption statuses for a meal plan
  Future<bool> clearMealConsumptionStatuses(String mealPlanId) async {
    try {
      final allKeys = _prefs.getKeys();
      final keysToRemove = allKeys.where((key) =>
        key.startsWith('$_mealConsumptionPrefix${mealPlanId}_')).toList();

      for (final key in keysToRemove) {
        await _prefs.remove(key);
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Migrate consumption data from separate storage to embedded meal plan structure
  Map<String, dynamic> _migrateConsumptionDataToMealPlan(Map<String, dynamic> mealPlanData) {
    try {
      // Check if meal plan already has embedded consumption data
      final days = mealPlanData['days'] as List<dynamic>?;
      if (days == null || days.isEmpty) return mealPlanData;

      // Check if first meal already has consumption data
      final firstDay = days.first as Map<String, dynamic>;
      final meals = firstDay['meals'] as List<dynamic>?;
      if (meals != null && meals.isNotEmpty) {
        final firstMeal = meals.first as Map<String, dynamic>;
        if (firstMeal.containsKey('isConsumed')) {
          // Already migrated
          return mealPlanData;
        }
      }

      // Generate meal plan ID for looking up consumption data
      final mealPlanId = _generateMealPlanIdFromData(mealPlanData);

      // Load existing consumption data
      final consumptionData = loadAllMealConsumptionStatuses(mealPlanId);

      // Create a copy of the meal plan data for modification
      final migratedData = Map<String, dynamic>.from(mealPlanData);
      final migratedDays = <Map<String, dynamic>>[];

      for (final dayData in days) {
        final day = Map<String, dynamic>.from(dayData as Map<String, dynamic>);
        final dayMeals = day['meals'] as List<dynamic>?;

        if (dayMeals != null) {
          final migratedMeals = <Map<String, dynamic>>[];

          for (final mealData in dayMeals) {
            final meal = Map<String, dynamic>.from(mealData as Map<String, dynamic>);

            // Generate consumption key for this meal
            final mealDate = DateTime.parse(day['date'] as String);
            final dateString = mealDate.toIso8601String().split('T')[0];
            final mealType = meal['type'] as String;
            final consumptionKey = '${mealPlanId}_${dateString}_${mealType}';

            // Check if consumption data exists for this meal
            final consumptionStatus = consumptionData[consumptionKey];
            if (consumptionStatus != null) {
              meal['isConsumed'] = consumptionStatus['isConsumed'] ?? false;
              if (consumptionStatus['consumedAt'] != null) {
                meal['consumedAt'] = consumptionStatus['consumedAt'];
              }
            } else {
              // Set default values
              meal['isConsumed'] = false;
              meal['consumedAt'] = null;
            }

            migratedMeals.add(meal);
          }

          day['meals'] = migratedMeals;
        }

        migratedDays.add(day);
      }

      migratedData['days'] = migratedDays;
      return migratedData;
    } catch (e) {
      // If migration fails, return original data
      return mealPlanData;
    }
  }

  /// Generate meal plan ID from meal plan data (for migration purposes)
  String _generateMealPlanIdFromData(Map<String, dynamic> mealPlanData) {
    try {
      final days = mealPlanData['days'] as List<dynamic>?;
      if (days != null && days.isNotEmpty) {
        final firstDay = days.first as Map<String, dynamic>;
        final dateStr = firstDay['date'] as String?;
        if (dateStr != null) {
          final date = DateTime.parse(dateStr);
          return 'meal_plan_${date.toIso8601String().split('T')[0]}';
        }
      }
      return 'meal_plan_${DateTime.now().toIso8601String().split('T')[0]}';
    } catch (e) {
      return 'meal_plan_${DateTime.now().toIso8601String().split('T')[0]}';
    }
  }

  /// Clean up old consumption data after migration
  Future<void> _cleanupOldConsumptionData(Map<String, dynamic> mealPlanData) async {
    try {
      final mealPlanId = _generateMealPlanIdFromData(mealPlanData);
      await clearMealConsumptionStatuses(mealPlanId);
    } catch (e) {
      // Ignore cleanup errors
    }
  }

  // Water Intake Tracking Methods

  /// Save daily water intake data
  Future<bool> saveDailyWaterIntake(String date, Map<String, dynamic> waterIntake) async {
    try {
      final jsonString = jsonEncode(waterIntake);
      return await _prefs.setString('$_waterIntakePrefix$date', jsonString);
    } catch (e) {
      return false;
    }
  }

  /// Load daily water intake data
  Map<String, dynamic>? loadDailyWaterIntake(String date) {
    try {
      final jsonString = _prefs.getString('$_waterIntakePrefix$date');
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Load water intake data for multiple dates
  Map<String, Map<String, dynamic>> loadWaterIntakeForDateRange(List<String> dates) {
    try {
      final Map<String, Map<String, dynamic>> waterIntakeData = {};

      for (final date in dates) {
        final data = loadDailyWaterIntake(date);
        if (data != null) {
          waterIntakeData[date] = data;
        }
      }

      return waterIntakeData;
    } catch (e) {
      return {};
    }
  }

  /// Remove daily water intake data
  Future<bool> removeDailyWaterIntake(String date) async {
    try {
      return await _prefs.remove('$_waterIntakePrefix$date');
    } catch (e) {
      return false;
    }
  }

  /// Save water intake settings
  Future<bool> saveWaterSettings(Map<String, dynamic> settings) async {
    try {
      final jsonString = jsonEncode(settings);
      return await _prefs.setString(_waterSettingsKey, jsonString);
    } catch (e) {
      return false;
    }
  }

  /// Load water intake settings
  Map<String, dynamic>? loadWaterSettings() {
    try {
      final jsonString = _prefs.getString(_waterSettingsKey);
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Clear water intake settings
  Future<bool> clearWaterSettings() async {
    try {
      await _prefs.remove(_waterSettingsKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get all water intake dates (for history/statistics)
  List<String> getAllWaterIntakeDates() {
    try {
      final allKeys = _prefs.getKeys();
      final waterIntakeDates = allKeys
          .where((key) => key.startsWith(_waterIntakePrefix))
          .map((key) => key.replaceFirst(_waterIntakePrefix, ''))
          .toList();

      // Sort dates in descending order (newest first)
      waterIntakeDates.sort((a, b) => b.compareTo(a));

      return waterIntakeDates;
    } catch (e) {
      return [];
    }
  }

  /// Clear all water intake data
  Future<bool> clearAllWaterIntakeData() async {
    try {
      final allKeys = _prefs.getKeys();
      final keysToRemove = allKeys.where((key) =>
        key.startsWith(_waterIntakePrefix)).toList();

      for (final key in keysToRemove) {
        await _prefs.remove(key);
      }

      return true;
    } catch (e) {
      return false;
    }
  }



  // Shopping List Methods (New Consolidated Approach)

  /// Save all shopping lists to consolidated storage
  Future<bool> saveAllShoppingLists(List<Map<String, dynamic>> shoppingLists) async {
    try {
      final jsonString = jsonEncode(shoppingLists);
      return await _prefs.setString(_shoppingListsKey, jsonString);
    } catch (e) {
      return false;
    }
  }

  /// Load all shopping lists from consolidated storage
  List<Map<String, dynamic>> loadAllShoppingLists() {
    try {
      // First, try to load from new consolidated storage
      final jsonString = _prefs.getString(_shoppingListsKey);
      if (jsonString != null) {
        final decoded = jsonDecode(jsonString);
        if (decoded is List) {
          final shoppingLists = decoded.cast<Map<String, dynamic>>();

          // Sort by creation date (newest first)
          shoppingLists.sort((a, b) {
            try {
              final aCreatedAt = DateTime.parse(a['createdAt'] as String);
              final bCreatedAt = DateTime.parse(b['createdAt'] as String);
              return bCreatedAt.compareTo(aCreatedAt);
            } catch (e) {
              // Fallback to id comparison if createdAt parsing fails
              final aId = a['id'] as String? ?? '';
              final bId = b['id'] as String? ?? '';
              return bId.compareTo(aId);
            }
          });

          return shoppingLists;
        }
      }

      // If no consolidated data, try to migrate from old format
      return _migrateFromLegacyStorage();
    } catch (e) {
      // If consolidated storage fails, try legacy migration
      return _migrateFromLegacyStorage();
    }
  }

  /// Add a new shopping list to consolidated storage
  Future<bool> addShoppingList(Map<String, dynamic> shoppingList) async {
    try {
      final currentLists = loadAllShoppingLists();

      // Check if shopping list already exists (by id)
      final existingIndex = currentLists.indexWhere(
        (list) => list['id'] == shoppingList['id']
      );

      if (existingIndex >= 0) {
        // Update existing shopping list
        currentLists[existingIndex] = shoppingList;
      } else {
        // Add new shopping list
        currentLists.add(shoppingList);
      }

      return await saveAllShoppingLists(currentLists);
    } catch (e) {
      return false;
    }
  }

  /// Update an existing shopping list in consolidated storage
  Future<bool> updateShoppingList(Map<String, dynamic> updatedShoppingList) async {
    try {
      final currentLists = loadAllShoppingLists();
      final listId = updatedShoppingList['id'] as String;

      final existingIndex = currentLists.indexWhere(
        (list) => list['id'] == listId
      );

      if (existingIndex >= 0) {
        currentLists[existingIndex] = updatedShoppingList;
        return await saveAllShoppingLists(currentLists);
      }

      return false; // Shopping list not found
    } catch (e) {
      return false;
    }
  }

  /// Remove a shopping list from consolidated storage
  Future<bool> removeShoppingList(String shoppingListId) async {
    try {
      final currentLists = loadAllShoppingLists();
      final initialLength = currentLists.length;

      currentLists.removeWhere((list) => list['id'] == shoppingListId);

      if (currentLists.length < initialLength) {
        return await saveAllShoppingLists(currentLists);
      }

      return false; // Shopping list not found
    } catch (e) {
      return false;
    }
  }

  /// Get a specific shopping list by ID
  Map<String, dynamic>? getShoppingListById(String shoppingListId) {
    try {
      final allLists = loadAllShoppingLists();
      return allLists.firstWhere(
        (list) => list['id'] == shoppingListId,
        orElse: () => <String, dynamic>{},
      );
    } catch (e) {
      return null;
    }
  }

  /// Clear all shopping list data (both new and legacy)
  Future<bool> clearAllShoppingListData() async {
    try {
      // Clear new consolidated storage
      await _prefs.remove(_shoppingListsKey);

      // Clear legacy storage
      final allKeys = _prefs.getKeys();
      final legacyKeysToRemove = allKeys.where((key) =>
        key.startsWith(_shoppingListPrefix)).toList();

      for (final key in legacyKeysToRemove) {
        await _prefs.remove(key);
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Migrate shopping lists from legacy date-based storage to consolidated storage
  List<Map<String, dynamic>> _migrateFromLegacyStorage() {
    try {
      final allKeys = _prefs.getKeys();
      final legacyShoppingListKeys = allKeys
          .where((key) => key.startsWith(_shoppingListPrefix))
          .toList();

      if (legacyShoppingListKeys.isEmpty) {
        return [];
      }

      final shoppingLists = <Map<String, dynamic>>[];

      for (final key in legacyShoppingListKeys) {
        final jsonString = _prefs.getString(key);
        if (jsonString != null) {
          try {
            final shoppingListData = jsonDecode(jsonString) as Map<String, dynamic>;
            shoppingLists.add(shoppingListData);
          } catch (e) {
            // Skip invalid JSON data
            continue;
          }
        }
      }

      // Sort by creation date (newest first)
      shoppingLists.sort((a, b) {
        try {
          final aCreatedAt = DateTime.parse(a['createdAt'] as String);
          final bCreatedAt = DateTime.parse(b['createdAt'] as String);
          return bCreatedAt.compareTo(aCreatedAt);
        } catch (e) {
          // Fallback to id comparison if createdAt parsing fails
          final aId = a['id'] as String? ?? '';
          final bId = b['id'] as String? ?? '';
          return bId.compareTo(aId);
        }
      });

      // Save migrated data to new consolidated storage
      if (shoppingLists.isNotEmpty) {
        saveAllShoppingLists(shoppingLists);

        // Optionally remove legacy data after successful migration
        // Commented out for safety - can be enabled later
        // _clearLegacyShoppingListData();
      }

      return shoppingLists;
    } catch (e) {
      return [];
    }
  }

  /// Clear only legacy shopping list data (for cleanup after migration)
  Future<bool> _clearLegacyShoppingListData() async {
    try {
      final allKeys = _prefs.getKeys();
      final legacyKeysToRemove = allKeys.where((key) =>
        key.startsWith(_shoppingListPrefix)).toList();

      for (final key in legacyKeysToRemove) {
        await _prefs.remove(key);
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  // Shopping List Loading Flag Methods

  /// Set shopping list loading flag with timestamp
  Future<bool> setShoppingListLoadingFlag() async {
    try {
      final flagData = {
        'started_at': DateTime.now().toIso8601String(),
      };
      final jsonString = jsonEncode(flagData);
      return await _prefs.setString(_generatingShoppingListKey, jsonString);
    } catch (e) {
      return false;
    }
  }

  /// Get shopping list loading flag data
  Map<String, dynamic>? getShoppingListLoadingFlag() {
    try {
      final jsonString = _prefs.getString(_generatingShoppingListKey);
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Remove shopping list loading flag
  Future<bool> removeShoppingListLoadingFlag() async {
    try {
      await _prefs.remove(_generatingShoppingListKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Check if shopping list loading flag is expired (older than 5 minutes)
  bool isShoppingListLoadingFlagExpired() {
    try {
      final flagData = getShoppingListLoadingFlag();
      if (flagData == null) return false;

      final startedAtString = flagData['started_at'] as String?;
      if (startedAtString == null) return true;

      final startedAt = DateTime.parse(startedAtString);
      final now = DateTime.now();
      final difference = now.difference(startedAt);

      return difference.inMinutes >= 5;
    } catch (e) {
      return true; // Consider expired if we can't parse
    }
  }

  // Legacy Methods (Deprecated - kept for backward compatibility)

  /// Save shopping list data (Legacy method - use addShoppingList instead)
  @Deprecated('Use addShoppingList instead')
  Future<bool> saveShoppingList(String date, Map<String, dynamic> shoppingList) async {
    return await addShoppingList(shoppingList);
  }

  /// Load shopping list data (Legacy method - use getShoppingListById instead)
  @Deprecated('Use getShoppingListById instead')
  Map<String, dynamic>? loadShoppingList(String date) {
    // Try to find a shopping list created on the given date
    final allLists = loadAllShoppingLists();
    try {
      return allLists.firstWhere((list) {
        final createdAt = DateTime.parse(list['createdAt'] as String);
        final createdDate = createdAt.toIso8601String().split('T')[0];
        return createdDate == date;
      });
    } catch (e) {
      return null;
    }
  }

  // Shopping List Generation Flag Methods

  /// Set the generating shopping list flag with current timestamp
  Future<bool> setGeneratingShoppingListFlag() async {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      return await _prefs.setInt(_generatingShoppingListKey, timestamp);
    } catch (e) {
      return false;
    }
  }

  /// Get the generating shopping list flag timestamp
  int? getGeneratingShoppingListFlag() {
    try {
      return _prefs.getInt(_generatingShoppingListKey);
    } catch (e) {
      return null;
    }
  }

  /// Check if shopping list is currently being generated
  bool isGeneratingShoppingList() {
    final timestamp = getGeneratingShoppingListFlag();
    if (timestamp == null) return false;

    // Check if flag is older than 5 minutes (automatic cleanup)
    final flagTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final now = DateTime.now();
    final difference = now.difference(flagTime);

    if (difference.inMinutes >= 5) {
      // Flag is stale, remove it
      removeGeneratingShoppingListFlag();
      return false;
    }

    return true;
  }

  /// Remove the generating shopping list flag
  Future<bool> removeGeneratingShoppingListFlag() async {
    try {
      return await _prefs.remove(_generatingShoppingListKey);
    } catch (e) {
      return false;
    }
  }

  /// Check if a shopping list was created after the generation flag timestamp
  bool wasShoppingListCreatedAfterFlag() {
    final flagTimestamp = getGeneratingShoppingListFlag();
    if (flagTimestamp == null) return false;

    final allLists = loadAllShoppingLists();
    if (allLists.isEmpty) return false;

    try {
      // Get the most recent shopping list
      final mostRecentList = allLists.first; // Already sorted by creation date (newest first)
      final createdAt = DateTime.parse(mostRecentList['createdAt'] as String);
      final flagTime = DateTime.fromMillisecondsSinceEpoch(flagTimestamp);

      return createdAt.isAfter(flagTime);
    } catch (e) {
      return false;
    }
  }

  // Weight Tracking Methods

  /// Save weight tracking data
  Future<bool> saveWeightTrackingData(Map<String, dynamic> weightData) async {
    try {
      final jsonString = jsonEncode(weightData);
      return await _prefs.setString(_weightTrackingKey, jsonString);
    } catch (e) {
      return false;
    }
  }

  /// Load weight tracking data
  Map<String, dynamic>? loadWeightTrackingData() {
    try {
      final jsonString = _prefs.getString(_weightTrackingKey);
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Save weight tracking settings
  Future<bool> saveWeightSettings(Map<String, dynamic> settings) async {
    try {
      final jsonString = jsonEncode(settings);
      return await _prefs.setString(_weightSettingsKey, jsonString);
    } catch (e) {
      return false;
    }
  }

  /// Load weight tracking settings
  Map<String, dynamic>? loadWeightSettings() {
    try {
      final jsonString = _prefs.getString(_weightSettingsKey);
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Clear weight tracking data
  Future<bool> clearWeightTrackingData() async {
    try {
      await _prefs.remove(_weightTrackingKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Clear weight tracking settings
  Future<bool> clearWeightSettings() async {
    try {
      await _prefs.remove(_weightSettingsKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Clear all weight tracking data and settings
  Future<bool> clearAllWeightTrackingData() async {
    try {
      await _prefs.remove(_weightTrackingKey);
      await _prefs.remove(_weightSettingsKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Notification Methods

  /// Save notifications for a specific date
  Future<bool> saveNotificationsForDate(String date, List<Map<String, dynamic>> notifications) async {
    try {
      final jsonString = jsonEncode(notifications);
      return await _prefs.setString('$_notificationsPrefix$date', jsonString);
    } catch (e) {
      return false;
    }
  }

  /// Load notifications for a specific date
  List<Map<String, dynamic>>? loadNotificationsForDate(String date) {
    try {
      final jsonString = _prefs.getString('$_notificationsPrefix$date');
      if (jsonString != null) {
        final dynamic decoded = jsonDecode(jsonString);
        if (decoded is List) {
          return decoded.cast<Map<String, dynamic>>();
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Save notification statistics
  Future<bool> saveNotificationStats(Map<String, dynamic> stats) async {
    try {
      final jsonString = jsonEncode(stats);
      return await _prefs.setString(_notificationStatsKey, jsonString);
    } catch (e) {
      return false;
    }
  }

  /// Load notification statistics
  Map<String, dynamic>? loadNotificationStats() {
    try {
      final jsonString = _prefs.getString(_notificationStatsKey);
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Get all notification dates (for pagination)
  List<String> getAllNotificationDates() {
    try {
      final allKeys = _prefs.getKeys();
      final notificationKeys = allKeys
          .where((key) => key.startsWith(_notificationsPrefix))
          .map((key) => key.replaceFirst(_notificationsPrefix, ''))
          .toList();

      // Sort dates in descending order (newest first)
      notificationKeys.sort((a, b) => b.compareTo(a));
      return notificationKeys;
    } catch (e) {
      return [];
    }
  }

  /// Clear all notifications
  Future<bool> clearAllNotifications() async {
    try {
      final allKeys = _prefs.getKeys();
      final notificationKeys = allKeys.where((key) => key.startsWith(_notificationsPrefix));

      for (final key in notificationKeys) {
        await _prefs.remove(key);
      }

      await _prefs.remove(_notificationStatsKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Home Layout Methods

  /// Save home layout mode preference
  Future<bool> saveHomeLayoutMode(String layoutMode) async {
    try {
      return await _prefs.setString(_homeLayoutModeKey, layoutMode);
    } catch (e) {
      return false;
    }
  }

  /// Load home layout mode preference
  String? loadHomeLayoutMode() {
    try {
      return _prefs.getString(_homeLayoutModeKey);
    } catch (e) {
      return null;
    }
  }

  // Meal Generation Loading Flag Methods

  /// Save meal generation loading flag with timestamp
  Future<bool> saveMealGenerationLoading(bool isLoading) async {
    try {
      if (isLoading) {
        final loadingData = {
          'isLoading': true,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
        final jsonString = jsonEncode(loadingData);
        return await _prefs.setString(_mealGenerationLoadingKey, jsonString);
      } else {
        // Remove the loading flag
        await _prefs.remove(_mealGenerationLoadingKey);
        return true;
      }
    } catch (e) {
      return false;
    }
  }

  /// Load meal generation loading flag
  Map<String, dynamic>? loadMealGenerationLoading() {
    try {
      final jsonString = _prefs.getString(_mealGenerationLoadingKey);
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Check if meal generation is currently loading
  bool isMealGenerationLoading() {
    final loadingData = loadMealGenerationLoading();
    if (loadingData == null) return false;

    final isLoading = loadingData['isLoading'] as bool? ?? false;
    final timestamp = loadingData['timestamp'] as int? ?? 0;

    // Check if loading flag is older than 10 minutes (timeout)
    final now = DateTime.now().millisecondsSinceEpoch;
    final tenMinutesInMs = 10 * 60 * 1000;

    if (now - timestamp > tenMinutesInMs) {
      // Loading flag is too old, remove it
      saveMealGenerationLoading(false);
      return false;
    }

    return isLoading;
  }

  /// Clear meal generation loading flag
  Future<bool> clearMealGenerationLoading() async {
    return await saveMealGenerationLoading(false);
  }

  // Meal Generation Error Methods

  /// Save meal generation error message
  Future<bool> saveMealGenerationError(String errorMessage) async {
    try {
      final errorData = {
        'error_message': errorMessage,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      final jsonString = jsonEncode(errorData);
      return await _prefs.setString(_mealGenerationErrorKey, jsonString);
    } catch (e) {
      return false;
    }
  }

  /// Load meal generation error data
  Map<String, dynamic>? loadMealGenerationError() {
    try {
      final jsonString = _prefs.getString(_mealGenerationErrorKey);
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Clear meal generation error
  Future<bool> clearMealGenerationError() async {
    try {
      await _prefs.remove(_mealGenerationErrorKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Check if there's a meal generation error
  bool hasMealGenerationError() {
    final errorData = loadMealGenerationError();
    return errorData != null && errorData['error_message'] != null;
  }

  // Meal Replacement Loading Flag Methods

  /// Save meal replacement loading flag with timestamp
  /// Key format: meal_replacement_loading_{mealDate}_{mealId}
  Future<bool> saveMealReplacementLoading(String mealDate, String mealId, bool isLoading) async {
    try {
      final key = 'meal_replacement_loading_${mealDate}_$mealId';

      if (isLoading) {
        final loadingData = {
          'isLoading': true,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'mealDate': mealDate,
          'mealId': mealId,
        };
        final jsonString = jsonEncode(loadingData);
        return await _prefs.setString(key, jsonString);
      } else {
        // Remove the loading flag
        await _prefs.remove(key);
        return true;
      }
    } catch (e) {
      return false;
    }
  }

  /// Load meal replacement loading flag
  Map<String, dynamic>? loadMealReplacementLoading(String mealDate, String mealId) {
    try {
      final key = 'meal_replacement_loading_${mealDate}_$mealId';
      final jsonString = _prefs.getString(key);
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Check if meal replacement is currently loading
  bool isMealReplacementLoading(String mealDate, String mealId) {
    final loadingData = loadMealReplacementLoading(mealDate, mealId);
    if (loadingData == null) return false;

    final isLoading = loadingData['isLoading'] as bool? ?? false;
    final timestamp = loadingData['timestamp'] as int? ?? 0;

    // Check if loading flag is older than 5 minutes (timeout)
    final now = DateTime.now().millisecondsSinceEpoch;
    final fiveMinutesInMs = 5 * 60 * 1000;

    if (now - timestamp > fiveMinutesInMs) {
      // Loading flag is too old, remove it
      saveMealReplacementLoading(mealDate, mealId, false);
      return false;
    }

    return isLoading;
  }

  /// Clear meal replacement loading flag
  Future<bool> clearMealReplacementLoading(String mealDate, String mealId) async {
    return await saveMealReplacementLoading(mealDate, mealId, false);
  }

  /// Clear all meal replacement loading flags (cleanup method)
  Future<bool> clearAllMealReplacementLoadingFlags() async {
    try {
      final allKeys = _prefs.getKeys();
      final replacementKeys = allKeys.where((key) => key.startsWith('meal_replacement_loading_'));

      for (final key in replacementKeys) {
        await _prefs.remove(key);
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  // ============================================================================
  // SECURE STORAGE METHODS (for sensitive data like tokens)
  // ============================================================================

  /// Store sensitive data securely
  Future<void> storeSecure(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }

  /// Retrieve sensitive data securely
  Future<String?> getSecure(String key) async {
    return await _secureStorage.read(key: key);
  }

  /// Store object securely as JSON
  Future<void> storeSecureObject(String key, Map<String, dynamic> object) async {
    final jsonString = jsonEncode(object);
    await storeSecure(key, jsonString);
  }

  /// Retrieve object securely from JSON
  Future<Map<String, dynamic>?> getSecureObject(String key) async {
    final jsonString = await getSecure(key);
    if (jsonString == null) return null;

    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  /// Delete secure data
  Future<void> deleteSecure(String key) async {
    await _secureStorage.delete(key: key);
  }

  /// Clear all secure data
  Future<void> clearSecure() async {
    await _secureStorage.deleteAll();
  }

  // ============================================================================
  // REGULAR STORAGE METHODS (for non-sensitive data)
  // ============================================================================

  /// Store string value
  Future<bool> setString(String key, String value) async {
    return await _prefs.setString(key, value);
  }

  /// Get string value
  String? getString(String key) {
    return _prefs.getString(key);
  }

  /// Store boolean value
  Future<bool> setBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }

  /// Get boolean value
  bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  /// Store integer value
  Future<bool> setInt(String key, int value) async {
    return await _prefs.setInt(key, value);
  }

  /// Get integer value
  int? getInt(String key) {
    return _prefs.getInt(key);
  }

  /// Store double value
  Future<bool> setDouble(String key, double value) async {
    return await _prefs.setDouble(key, value);
  }

  /// Get double value
  double? getDouble(String key) {
    return _prefs.getDouble(key);
  }

  /// Store object as JSON
  Future<bool> setObject(String key, Map<String, dynamic> object) async {
    final jsonString = jsonEncode(object);
    return await setString(key, jsonString);
  }

  /// Get object from JSON
  Map<String, dynamic>? getObject(String key) {
    final jsonString = getString(key);
    if (jsonString == null) return null;

    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }



  /// Clear all non-secure data
  Future<bool> clear() async {
    return await _prefs.clear();
  }



  // ============================================================================
  // CONVENIENCE METHODS FOR APP-SPECIFIC KEYS
  // ============================================================================

  /// Store user token securely
  Future<void> storeUserToken(String token) async {
    await storeSecure('user_token', token);
  }

  /// Get user token
  Future<String?> getUserToken() async {
    return await getSecure('user_token');
  }

  /// Store user profile securely
  Future<void> storeUserProfile(Map<String, dynamic> profile) async {
    await storeSecureObject('user_profile', profile);
  }

  /// Get user profile
  Future<Map<String, dynamic>?> getUserProfile() async {
    return await getSecureObject('user_profile');
  }

  /// Set language preference
  Future<bool> setLanguage(String languageCode) async {
    return await setString('language', languageCode);
  }

  /// Get language preference
  String getLanguage() {
    return getString('language') ?? 'ar';
  }

  /// Set theme preference
  Future<bool> setTheme(String theme) async {
    return await setString('theme', theme);
  }

  /// Get theme preference
  String getTheme() {
    return getString('theme') ?? 'system';
  }

}

@riverpod
Future<LocalStorageService> localStorageService(LocalStorageServiceRef ref) async {
  final prefs = await SharedPreferences.getInstance();
  return LocalStorageService(prefs);
}
