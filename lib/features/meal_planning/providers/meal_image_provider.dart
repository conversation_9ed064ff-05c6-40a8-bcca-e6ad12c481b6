import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/services/local_storage_service.dart';
import '../data/services/meal_plan_service.dart';
import 'current_meal_plan_provider.dart';

part 'meal_image_provider.freezed.dart';
part 'meal_image_provider.g.dart';

@freezed
class MealImageState with _$MealImageState {
  const factory MealImageState({
    @Default(false) bool isGenerating,
    @Default(null) String? error,
    @Default(null) String? generatingMealId,
  }) = _MealImageState;
}

@riverpod
class MealImageNotifier extends _$MealImageNotifier {
  StreamSubscription<DocumentSnapshot>? _imageTrackerSubscription;
  Timer? _fallbackTimer;

  @override
  MealImageState build() {
    return const MealImageState();
  }

  /// Generate image for a meal
  Future<void> generateMealImage({
    String? dayDocumentId,
    String? mealDate,
    required String mealId,
  }) async {
    try {
      // Set generating state
      state = state.copyWith(
        isGenerating: true,
        error: null,
        generatingMealId: mealId,
      );

      // Save loading flag to local storage
      await _saveMealImageGenerationLoading(mealId);

      // Start listening for image generation tracker updates
      _startMealImageGenerationListener(mealId);

      // Start fallback timer to check for completion every 30 seconds
      _startFallbackTimer(mealId);

      // Call Firebase function to generate image
      final mealPlanService = await ref.read(mealPlanServiceProvider.future);
      await mealPlanService.generateMealImage(
        dayDocumentId: dayDocumentId,
        mealDate: mealDate,
        mealId: mealId,
      );

      debugPrint('Meal image generation started for meal: $mealId, dayDocumentId: $dayDocumentId, mealDate: $mealDate');
    } catch (e) {
      debugPrint('Error generating meal image: $e');
      
      // Clear loading state on error
      state = state.copyWith(
        isGenerating: false,
        error: e.toString(),
        generatingMealId: null,
      );

      // Clear loading flag from local storage
      await _clearMealImageGenerationLoading(mealId);

      // Cancel fallback timer
      _fallbackTimer?.cancel();
    }
  }

  /// Start listening for image generation tracker updates
  void _startMealImageGenerationListener(String mealId) {
    final userId = FirebaseAuth.instance.currentUser?.uid;
    if (userId == null) return;

    _imageTrackerSubscription?.cancel();
    _imageTrackerSubscription = FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('bjt_meal_image_generating')
        .doc('meal_$mealId')
        .snapshots()
        .listen((snapshot) {
      if (snapshot.exists) {
        _handleMealImageGenerationUpdate(mealId, snapshot.data()!);
      }
    });
  }

  /// Handle meal image generation update from background job tracker
  Future<void> _handleMealImageGenerationUpdate(String mealId, Map<String, dynamic> data) async {
    try {
      final lastCompletionTimestamp = data['last_completion_datetime'] as Timestamp?;
      if (lastCompletionTimestamp == null) {
        debugPrint('MealImageNotifier: No completion datetime found for meal $mealId');
        return;
      }

      final completionDateTime = lastCompletionTimestamp.toDate();
      debugPrint('MealImageNotifier: Image generation completed at: ${completionDateTime.toIso8601String()}');

      // Check if local loading flag exists
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final loadingData = localStorageService.loadMealImageGenerationLoading(mealId);

      if (loadingData == null) {
        debugPrint('MealImageNotifier: No local loading flag found for meal $mealId, ignoring update');
        return;
      }

      // Check if loading flag timestamp is less than completion datetime (document is more recent)
      final flagTimestampMs = loadingData['timestamp'] as int?;
      if (flagTimestampMs == null) {
        debugPrint('MealImageNotifier: Loading flag has no timestamp for meal $mealId, clearing flag');
        await _clearMealImageGenerationLoading(mealId);
        return;
      }

      final flagTimestamp = DateTime.fromMillisecondsSinceEpoch(flagTimestampMs);
      if (!completionDateTime.isAfter(flagTimestamp)) {
        debugPrint('MealImageNotifier: Image generation completion is not newer than loading flag for meal $mealId, ignoring');
        return;
      }

      // Document is more recent than local flag, clear the loading flag
      await _clearMealImageGenerationLoading(mealId);
      debugPrint('MealImageNotifier: Cleared local loading flag for meal $mealId');

      // Check the status and handle accordingly
      final status = data['status'] as String?;
      final imageUrl = data['image_url'] as String?;

      if (status == 'completed' && imageUrl != null) {
        // Update UI state
        state = state.copyWith(
          isGenerating: false,
          error: null,
          generatingMealId: null,
        );

        // Trigger a refresh of the current meal plan to get the updated meal data with image
        ref.invalidate(currentMealPlanNotifierProvider);

        // Cancel fallback timer since generation is complete
        _fallbackTimer?.cancel();

        debugPrint('MealImageNotifier: Successfully updated meal image for $mealId');
      } else if (status == 'failed') {
        // Update UI state to show failure
        state = state.copyWith(
          isGenerating: false,
          error: 'فشل في إنشاء صورة الوجبة',
          generatingMealId: null,
        );

        // Cancel fallback timer
        _fallbackTimer?.cancel();

        debugPrint('MealImageNotifier: Image generation failed for meal $mealId');
      }
    } catch (e) {
      debugPrint('MealImageNotifier: Error handling meal image generation update: $e');
    }
  }

  /// Save meal image generation loading flag to local storage
  Future<void> _saveMealImageGenerationLoading(String mealId) async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final loadingData = {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'meal_id': mealId,
      };

      await localStorageService.setObject('meal_image_generation_$mealId', loadingData);
      debugPrint('Saved meal image generation loading flag for meal: $mealId');
    } catch (e) {
      debugPrint('Error saving meal image generation loading flag: $e');
    }
  }

  /// Clear meal image generation loading flag from local storage
  Future<void> _clearMealImageGenerationLoading(String mealId) async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.remove('meal_image_generation_$mealId');
      debugPrint('Cleared meal image generation loading flag for meal: $mealId');
    } catch (e) {
      debugPrint('Error clearing meal image generation loading flag: $e');
    }
  }

  /// Check if a specific meal is currently generating an image
  bool isMealGeneratingImage(String mealId) {
    return state.isGenerating && state.generatingMealId == mealId;
  }

  /// Initialize image generation loading state from local storage
  Future<void> initializeImageGenerationState(String mealId) async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final loadingData = localStorageService.loadMealImageGenerationLoading(mealId);

      if (loadingData == null) {
        // No loading flag, ensure state is not generating
        if (state.generatingMealId == mealId) {
          state = state.copyWith(
            isGenerating: false,
            generatingMealId: null,
          );
        }
        return;
      }

      final flagTimestamp = loadingData['timestamp'] as int?;
      if (flagTimestamp == null) {
        // Invalid loading data, clear it
        await _clearMealImageGenerationLoading(mealId);
        return;
      }

      final flagDatetime = DateTime.fromMillisecondsSinceEpoch(flagTimestamp);
      final now = DateTime.now();

      // If flag is older than 15 minutes, consider it stale and clear it
      if (now.difference(flagDatetime).inMinutes > 15) {
        await _clearMealImageGenerationLoading(mealId);
        return;
      }

      // Set generating state and start listening for updates
      state = state.copyWith(
        isGenerating: true,
        generatingMealId: mealId,
      );

      _startMealImageGenerationListener(mealId);
    } catch (e) {
      debugPrint('Error initializing image generation state: $e');
    }
  }

  /// Start fallback timer to periodically check for image completion
  void _startFallbackTimer(String mealId) {
    _fallbackTimer?.cancel();
    _fallbackTimer = Timer.periodic(const Duration(seconds: 30), (timer) async {
      // Check if we're still generating for this meal
      if (!state.isGenerating || state.generatingMealId != mealId) {
        timer.cancel();
        return;
      }

      // Trigger a refresh to check if the image has been generated
      ref.invalidate(currentMealPlanNotifierProvider);

      debugPrint('Fallback timer: Checking for image completion for meal: $mealId');
    });
  }

  /// Dispose resources
  void dispose() {
    _imageTrackerSubscription?.cancel();
    _fallbackTimer?.cancel();
  }
}

/// Extension for LocalStorageService to handle meal image generation loading
extension MealImageLocalStorage on LocalStorageService {
  /// Load meal image generation loading data
  Map<String, dynamic>? loadMealImageGenerationLoading(String mealId) {
    try {
      return getObject('meal_image_generation_$mealId');
    } catch (e) {
      debugPrint('Error loading meal image generation loading data: $e');
      return null;
    }
  }
}
