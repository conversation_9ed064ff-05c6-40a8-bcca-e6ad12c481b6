import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/services/local_storage_service.dart';
import '../../../core/utils/logger.dart';
import '../../../core/utils/timestamp_utils.dart';
import '../data/models/meal_plan_request.dart';
import '../data/services/meal_plan_service.dart';
import 'meal_plan_generation_provider.dart';

part '../providers/current_meal_plan_provider.freezed.dart';
part '../providers/current_meal_plan_provider.g.dart';

@freezed
class CurrentMealPlanState with _$CurrentMealPlanState {
  const factory CurrentMealPlanState({
    @Default(false) bool isLoading,
    @Default(false) bool isRefreshing,
    MealPlanResponse? currentPlan,
    String? error,
    DateTime? lastUpdated,
  }) = _CurrentMealPlanState;
}

@riverpod
class CurrentMealPlanNotifier extends _$CurrentMealPlanNotifier {
  StreamSubscription<DocumentSnapshot>? _backgroundJobSubscription;

  @override
  CurrentMealPlanState build() {
    // Load meal plan from local storage on initialization
    _loadFromLocalStorage();

    // Start listening for new meals from Firestore (async)
    Future.microtask(() => _startFirestoreListener());

    // Clean up subscription when provider is disposed
    ref.onDispose(() {
      _backgroundJobSubscription?.cancel();
    });

    return const CurrentMealPlanState();
  }

  /// Load meal plan from local storage
  /// This method ensures that nutrition information reflects selected meal versions
  Future<void> _loadFromLocalStorage() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);

      // Check for meal generation errors first
      final errorData = localStorageService.loadMealGenerationError();
      if (errorData != null) {
        final errorMessage = errorData['error_message'] as String? ?? 'حدث خطأ أثناء إنشاء خطة الوجبات';
        state = state.copyWith(error: errorMessage);
        return;
      }

      final mealPlanData = localStorageService.loadCurrentMealPlan();

      if (mealPlanData != null) {
        // Debug: Check if day_document_id exists in local storage data
        if (mealPlanData['days'] is List) {
          final days = mealPlanData['days'] as List;
          for (int i = 0; i < days.length; i++) {
            final dayData = days[i] as Map<String, dynamic>;
            AppLogger.info('CurrentMealPlanNotifier: Debug - Day $i local storage contains day_document_id: ${dayData['day_document_id']}');
          }
        }

        // Load the GeneratedMealPlan data and wrap it in a MealPlanResponse for compatibility
        final generatedMealPlan = GeneratedMealPlan.fromJson(mealPlanData);

        // Debug: Check if dayDocumentId is populated after fromJson
        for (int i = 0; i < generatedMealPlan.days.length; i++) {
          final day = generatedMealPlan.days[i];
          AppLogger.info('CurrentMealPlanNotifier: Debug - Day $i after fromJson has dayDocumentId: ${day.dayDocumentId}');
        }

        // Process the meal plan to ensure nutrition information matches selected meal versions
        final processedDays = generatedMealPlan.days.map((day) {
          final processedMeals = day.meals.map((meal) {
            // If a meal has a selected version, ensure its nutrition matches the active meal
            if (meal.selectedMealId != null) {
              final activeMeal = meal.getActiveMeal();
              return meal.copyWith(
                nutrition: activeMeal.nutrition,
                name: activeMeal.name,
                description: activeMeal.description,
                ingredients: activeMeal.ingredients,
                preparationTime: activeMeal.preparationTime,
                difficulty: activeMeal.difficulty,
              );
            }
            return meal;
          }).toList();

          return day.copyWith(meals: processedMeals);
        }).toList();

        final processedMealPlan = generatedMealPlan.copyWith(days: processedDays);
        final mealPlan = MealPlanResponse(
          success: true,
          message: 'خطة وجبات محملة من التخزين المحلي',
          mealPlan: processedMealPlan,
        );

        state = state.copyWith(
          currentPlan: mealPlan,
          lastUpdated: DateTime.now(),
        );
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to load meal plan: $e');
    }
  }

  /// Start listening for meal plan generation background job completion
  /// Monitors /users/{userId}/background_jobs/meal_plan_generation for completion
  Future<void> _startFirestoreListener() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    try {
      AppLogger.info('CurrentMealPlanNotifier: Starting Firestore listener for meal plan generation background job');

      // Listen to the meal plan generation background job document
      _backgroundJobSubscription = FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('background_jobs')
          .doc('meal_plan_generation')
          .snapshots()
          .listen(
        (snapshot) {
          _handleBackgroundJobUpdate(snapshot);
        },
        onError: (error) {
          AppLogger.error('CurrentMealPlanNotifier: Error listening to background job: $error');
        },
      );
    } catch (e) {
      AppLogger.error('CurrentMealPlanNotifier: Error setting up background job listener: $e');
    }
  }



  /// Handle background job document updates for meal plan generation
  Future<void> _handleBackgroundJobUpdate(DocumentSnapshot snapshot) async {
    try {
      if (!snapshot.exists) return;

      final data = snapshot.data() as Map<String, dynamic>?;
      if (data == null) return;

      AppLogger.info('CurrentMealPlanNotifier: Received background job update: ${data.keys}');

      // Check if the document was added or updated (has completed_at)
      final completedAtTimestamp = data['completed_at'] as Timestamp?;
      if (completedAtTimestamp == null) {
        AppLogger.info('CurrentMealPlanNotifier: Background job not completed yet');
        return;
      }

      final completedAt = completedAtTimestamp.toDate();
      AppLogger.info('CurrentMealPlanNotifier: Background job completed at: ${completedAt.toIso8601String()}');

      // Check if local loading flag exists
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final loadingData = localStorageService.loadMealGenerationLoading();

      if (loadingData == null) {
        AppLogger.info('CurrentMealPlanNotifier: No local loading flag found, ignoring background job update');
        return;
      }

      // Check if loading flag timestamp is less than completed_at (document is more recent)
      final flagTimestampMs = loadingData['timestamp'] as int?;
      if (flagTimestampMs == null) {
        AppLogger.warning('CurrentMealPlanNotifier: Loading flag has no timestamp, clearing flag');
        await localStorageService.clearMealGenerationLoading();
        return;
      }

      final flagTimestamp = DateTime.fromMillisecondsSinceEpoch(flagTimestampMs);
      if (!completedAt.isAfter(flagTimestamp)) {
        AppLogger.info('CurrentMealPlanNotifier: Background job completion is not newer than loading flag, ignoring');
        return;
      }

      // Document is more recent than local flag, clear the loading flag
      await localStorageService.clearMealGenerationLoading();
      AppLogger.info('CurrentMealPlanNotifier: Cleared local loading flag');

      // Check the status of the background job
      final status = data['status'] as String?;
      if (status != 'success') {
        // Handle failure case
        final failureMessage = data['failure_message'] as String? ?? 'حدث خطأ أثناء إنشاء خطة الوجبات';
        await localStorageService.saveMealGenerationError(failureMessage);
        AppLogger.error('CurrentMealPlanNotifier: Background job failed: $failureMessage');

        // Reload from local storage to update UI
        await _loadFromLocalStorage();
        return;
      }

      // Success case - fetch and merge new meal data
      await _fetchAndMergeNewMealData(completedAt);

    } catch (e) {
      AppLogger.error('CurrentMealPlanNotifier: Error handling background job update: $e');
    }
  }

  /// Fetch and merge new meal data from Firestore
  /// Gets all days with created_at >= completed_at timestamp
  Future<void> _fetchAndMergeNewMealData(DateTime completedAt) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      AppLogger.info('CurrentMealPlanNotifier: Fetching new meal data created after: ${completedAt.toIso8601String()}');

      // Query for days with created_at >= completed_at
      final querySnapshot = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('days')
          .where('created_at', isGreaterThanOrEqualTo: Timestamp.fromDate(completedAt))
          .orderBy('created_at', descending: false)
          .get();

      if (querySnapshot.docs.isEmpty) {
        AppLogger.info('CurrentMealPlanNotifier: No new meal data found');
        return;
      }

      AppLogger.info('CurrentMealPlanNotifier: Found ${querySnapshot.docs.length} new day documents');

      // Convert Firestore documents to DayMealPlan objects
      final newDays = <DayMealPlan>[];
      DateTime? latestDayDate;

      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();

          // Extract day date with proper Firestore timestamp handling
          DateTime dayDate;
          if (data['date'] is Timestamp) {
            dayDate = (data['date'] as Timestamp).toDate();
          } else if (data['date'] is String) {
            dayDate = DateTime.parse(data['date'] as String);
          } else {
            throw Exception('Invalid date format in day document: ${data['date']}');
          }

          if (latestDayDate == null || dayDate.isAfter(latestDayDate)) {
            latestDayDate = dayDate;
          }

          // Convert meals data with proper error handling
          final mealsData = data['meals'] as List<dynamic>? ?? [];
          final meals = <GeneratedMeal>[];

          for (final mealData in mealsData) {
            try {
              final mealMap = mealData as Map<String, dynamic>;

              // Convert Firestore timestamps in meal data
              final convertedMealData = _convertFirestoreTimestampsInMeal(mealMap);

              // Use compatibility method to handle missing fields and different formats
              final meal = GeneratedMeal.fromJsonWithCompatibility(convertedMealData);
              meals.add(meal);
            } catch (mealError) {
              AppLogger.warning('CurrentMealPlanNotifier: Failed to parse meal from document ${doc.id}: $mealError');
              // Skip this meal but continue processing other meals
            }
          }

          // Convert nutrition data with null safety
          final nutritionData = (data['totalNutrition'] ?? data['total_nutrition']) as Map<String, dynamic>? ?? {};
          final totalNutrition = _parseNutritionInfoSafely(nutritionData);

          // Create DayMealPlan
          final dayMealPlan = DayMealPlan(
            date: dayDate,
            meals: meals,
            totalNutrition: totalNutrition,
            dayDocumentId: doc.id, // Store the Firestore document ID
          );

          newDays.add(dayMealPlan);
          AppLogger.info('CurrentMealPlanNotifier: Processed day ${dayDate.toIso8601String()} with ${meals.length} meals');
        } catch (e) {
          AppLogger.error('CurrentMealPlanNotifier: Error processing document ${doc.id}: $e');
          // Continue processing other documents
        }
      }

      if (newDays.isEmpty) {
        AppLogger.info('CurrentMealPlanNotifier: No valid new days to merge');
        return;
      }

      // Sort new days by date
      newDays.sort((a, b) => a.date.compareTo(b.date));

      // Merge new days with local storage
      await _appendNewDaysToLocalStorage(newDays);

      // Reload from local storage to update UI
      await _loadFromLocalStorage();

      AppLogger.info('CurrentMealPlanNotifier: Successfully merged ${newDays.length} new days to local storage');
    } catch (e) {
      AppLogger.error('CurrentMealPlanNotifier: Error fetching and merging new meal data: $e');
    }
  }

  /// Append new days to existing local storage meal plan
  Future<void> _appendNewDaysToLocalStorage(List<DayMealPlan> newDays) async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final existingMealPlanData = localStorageService.loadCurrentMealPlan();

      if (existingMealPlanData != null) {
        // Load existing meal plan and append new days
        final existingMealPlan = GeneratedMealPlan.fromJson(existingMealPlanData);
        final allDays = [...existingMealPlan.days, ...newDays];

        // Remove duplicates based on date and sort
        final uniqueDays = <DayMealPlan>[];
        final seenDates = <String>{};

        for (final day in allDays) {
          final dateKey = day.date.toIso8601String().split('T')[0]; // YYYY-MM-DD
          if (!seenDates.contains(dateKey)) {
            seenDates.add(dateKey);
            uniqueDays.add(day);
          }
        }

        // Sort by date
        uniqueDays.sort((a, b) => a.date.compareTo(b.date));

        // Create updated meal plan
        final updatedMealPlan = existingMealPlan.copyWith(days: uniqueDays);

        // Save updated meal plan
        await localStorageService.saveCurrentMealPlan(updatedMealPlan.toJson());

        AppLogger.info('CurrentMealPlanNotifier: Appended ${newDays.length} new days to existing meal plan (total: ${uniqueDays.length} days)');
      } else {
        // No existing meal plan, create new one with just the new days
        final newMealPlan = GeneratedMealPlan(
          days: newDays,
        );

        await localStorageService.saveCurrentMealPlan(newMealPlan.toJson());

        AppLogger.info('CurrentMealPlanNotifier: Created new meal plan with ${newDays.length} days');
      }
    } catch (e) {
      AppLogger.error('CurrentMealPlanNotifier: Error appending new days to local storage: $e');
      throw e;
    }
  }

  /// Convert Firestore timestamps in meal data to ISO strings
  Map<String, dynamic> _convertFirestoreTimestampsInMeal(Map<String, dynamic> mealData) {
    final convertedData = Map<String, dynamic>.from(mealData);

    // Convert consumed_at timestamp if present
    if (convertedData['consumed_at'] != null) {
      convertedData['consumed_at'] = TimestampUtils.convertTimestampToString(convertedData['consumed_at']);
    }

    // Convert any other timestamp fields that might be present
    if (convertedData['created_at'] != null) {
      convertedData['created_at'] = TimestampUtils.convertTimestampToString(convertedData['created_at']);
    }

    if (convertedData['updated_at'] != null) {
      convertedData['updated_at'] = TimestampUtils.convertTimestampToString(convertedData['updated_at']);
    }

    return convertedData;
  }



  /// Parse NutritionInfo with null safety for numeric fields
  NutritionInfo _parseNutritionInfoSafely(Map<String, dynamic> nutritionData) {
    try {
      // Create a copy and ensure all numeric fields have valid values
      final safeData = Map<String, dynamic>.from(nutritionData);

      // List of numeric fields that should not be null (only fields that exist in NutritionInfo model)
      final numericFields = [
        'calories', 'protein', 'carbs', 'fat', 'fiber'
      ];

      for (final field in numericFields) {
        if (safeData[field] == null) {
          safeData[field] = 0.0;
        } else if (safeData[field] is! num) {
          // Try to parse as number, default to 0 if fails
          try {
            safeData[field] = double.parse(safeData[field].toString());
          } catch (e) {
            AppLogger.warning('CurrentMealPlanNotifier: Invalid numeric value for $field: ${safeData[field]}');
            safeData[field] = 0.0;
          }
        }
      }

      return NutritionInfo.fromJson(safeData);
    } catch (e) {
      AppLogger.warning('CurrentMealPlanNotifier: Error parsing nutrition data, using defaults: $e');
      // Return default nutrition info if parsing fails
      return const NutritionInfo(
        calories: 0,
        protein: 0.0,
        carbs: 0.0,
        fat: 0.0,
        fiber: 0.0,
      );
    }
  }

  /// Clear meal generation loading flag if it's outdated compared to latest day date
  Future<void> _clearOutdatedGenerationFlag(DateTime latestDayDate) async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);

      // Get the loading flag data
      final loadingData = localStorageService.loadMealGenerationLoading();
      if (loadingData == null) return;

      // Extract timestamp from loading data
      final timestampMs = loadingData['timestamp'] as int?;
      if (timestampMs == null) {
        // No timestamp available, clear the flag anyway
        await localStorageService.clearMealGenerationLoading();
        AppLogger.info('CurrentMealPlanNotifier: Cleared generation flag (no timestamp available)');
        return;
      }

      final generationTimestamp = DateTime.fromMillisecondsSinceEpoch(timestampMs);

      // Compare latest day date with generation flag timestamp
      if (latestDayDate.isAfter(generationTimestamp)) {
        // Latest day is newer than generation flag, so flag is outdated
        await localStorageService.clearMealGenerationLoading();
        AppLogger.info('CurrentMealPlanNotifier: Cleared outdated generation flag (latest day: ${latestDayDate.toIso8601String()}, flag timestamp: ${generationTimestamp.toIso8601String()})');
      } else {
        AppLogger.info('CurrentMealPlanNotifier: Generation flag is still valid (latest day: ${latestDayDate.toIso8601String()}, flag timestamp: ${generationTimestamp.toIso8601String()})');
      }
    } catch (e) {
      AppLogger.error('CurrentMealPlanNotifier: Error checking/clearing generation flag: $e');
    }
  }

  /// Save meal plan to local storage and update state
  /// This method ensures that nutrition information reflects selected meal versions
  Future<void> saveMealPlan(MealPlanResponse mealPlan) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Process the meal plan to ensure nutrition information matches selected meal versions
      final processedDays = mealPlan.mealPlan.days.map((day) {
        final processedMeals = day.meals.map((meal) {
          // If a meal has a selected version, ensure its nutrition matches the active meal
          if (meal.selectedMealId != null) {
            final activeMeal = meal.getActiveMeal();
            return meal.copyWith(
              nutrition: activeMeal.nutrition,
              name: activeMeal.name,
              description: activeMeal.description,
              ingredients: activeMeal.ingredients,
              preparationTime: activeMeal.preparationTime,
              difficulty: activeMeal.difficulty,
            );
          }
          return meal;
        }).toList();

        return day.copyWith(meals: processedMeals);
      }).toList();

      final processedMealPlan = mealPlan.copyWith(
        mealPlan: mealPlan.mealPlan.copyWith(days: processedDays),
      );

      final localStorageService = await ref.read(localStorageServiceProvider.future);
      // Save the processed meal plan data, not the entire response wrapper
      final success = await localStorageService.saveCurrentMealPlan(processedMealPlan.mealPlan.toJson());

      if (success) {
        state = state.copyWith(
          isLoading: false,
          currentPlan: processedMealPlan,
          lastUpdated: DateTime.now(),
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to save meal plan',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to save meal plan: $e',
      );
    }
  }

  /// Clear current meal plan
  Future<void> clearMealPlan() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.clearCurrentMealPlan();

      state = state.copyWith(
        currentPlan: null,
        lastUpdated: null,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(error: 'Failed to clear meal plan: $e');
    }
  }

  /// Get today's meals from current plan
  List<GeneratedMeal> getTodaysMeals() {
    final plan = state.currentPlan;
    if (plan == null) return [];

    final today = DateTime.now();
    final daysSinceLastUpdate = state.lastUpdated != null
        ? today.difference(state.lastUpdated!).inDays
        : 0;

    // Get the appropriate day from the meal plan
    final dayIndex = daysSinceLastUpdate % plan.mealPlan.days.length;

    if (dayIndex < plan.mealPlan.days.length) {
      return plan.mealPlan.days[dayIndex].meals;
    }

    return [];
  }

  /// Get meal by type for today
  GeneratedMeal? getTodaysMealByType(String mealType) {
    final todaysMeals = getTodaysMeals();
    try {
      return todaysMeals.firstWhere(
        (meal) => meal.type.toLowerCase() == mealType.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  /// Check if meal plan exists and is valid
  bool get hasMealPlan => state.currentPlan != null;

  /// Check if meal plan is recent (within last 7 days)
  bool get isMealPlanRecent {
    if (state.lastUpdated == null) return false;
    final daysSinceUpdate = DateTime.now().difference(state.lastUpdated!).inDays;
    return daysSinceUpdate <= 7;
  }

  /// Toggle meal consumption status
  Future<void> toggleMealConsumption({
    required DateTime mealDate,
    required String mealType,
  }) async {
    try {
      final currentPlan = state.currentPlan;
      if (currentPlan == null) return;

      // Find the day and meal to update
      final updatedDays = currentPlan.mealPlan.days.map((day) {
        if (_isSameDay(day.date, mealDate)) {
          final updatedMeals = day.meals.map((meal) {
            if (meal.type == mealType) {
              return meal.copyWith(
                isConsumed: !meal.isConsumed,
                consumedAt: !meal.isConsumed ? DateTime.now() : null,
              );
            }
            return meal;
          }).toList();

          return day.copyWith(meals: updatedMeals);
        }
        return day;
      }).toList();

      // Create updated meal plan
      final updatedMealPlan = currentPlan.mealPlan.copyWith(days: updatedDays);
      final updatedResponse = currentPlan.copyWith(mealPlan: updatedMealPlan);

      // Save to local storage
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.saveCurrentMealPlan(updatedMealPlan.toJson());

      // Update state
      state = state.copyWith(
        currentPlan: updatedResponse,
        lastUpdated: DateTime.now(),
      );

      // Sync with meal plan generation provider for UI consistency
      _syncWithMealPlanGenerationProvider(updatedResponse);

      // Sync specific day to Firestore in background (optimized for single meal updates)
      unawaited(_syncSingleDayToFirestore(mealDate));
    } catch (e) {
      state = state.copyWith(error: 'Failed to update meal consumption: $e');
    }
  }

  /// Clear all consumption statuses
  Future<void> clearAllConsumptionStatuses() async {
    try {
      final currentPlan = state.currentPlan;
      if (currentPlan == null) return;

      // Reset all consumption statuses
      final updatedDays = currentPlan.mealPlan.days.map((day) {
        final updatedMeals = day.meals.map((meal) {
          return meal.copyWith(
            isConsumed: false,
            consumedAt: null,
          );
        }).toList();

        return day.copyWith(meals: updatedMeals);
      }).toList();

      // Create updated meal plan
      final updatedMealPlan = currentPlan.mealPlan.copyWith(days: updatedDays);
      final updatedResponse = currentPlan.copyWith(mealPlan: updatedMealPlan);

      // Save to local storage
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.saveCurrentMealPlan(updatedMealPlan.toJson());

      // Update state
      state = state.copyWith(
        currentPlan: updatedResponse,
        lastUpdated: DateTime.now(),
      );

      // Sync to Firestore in background (full sync for clearing all consumption)
      unawaited(_syncToFirestore(updatedMealPlan));
    } catch (e) {
      state = state.copyWith(error: 'Failed to clear consumption statuses: $e');
    }
  }

  /// Replace a meal in the current meal plan
  Future<bool> replaceMealInPlan({
    required DateTime mealDate,
    required String mealType,
    required GeneratedMeal newMeal,
  }) async {
    try {
      final currentPlan = state.currentPlan;
      if (currentPlan == null) return false;

      // Find the day and meal to replace
      final updatedDays = currentPlan.mealPlan.days.map((day) {
        if (_isSameDay(day.date, mealDate)) {
          final updatedMeals = day.meals.map((meal) {
            if (meal.type == mealType) {
              return newMeal;
            }
            return meal;
          }).toList();

          return day.copyWith(meals: updatedMeals);
        }
        return day;
      }).toList();

      // Create updated meal plan
      final updatedMealPlan = currentPlan.mealPlan.copyWith(days: updatedDays);
      final updatedResponse = currentPlan.copyWith(mealPlan: updatedMealPlan);

      // Save to local storage
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.saveCurrentMealPlan(updatedMealPlan.toJson());

      // Update state
      state = state.copyWith(
        currentPlan: updatedResponse,
        lastUpdated: DateTime.now(),
      );

      // Sync with meal plan generation provider for UI consistency
      _syncWithMealPlanGenerationProvider(updatedResponse);

      // Sync specific day to Firestore in background (optimized for meal replacement)
      unawaited(_syncSingleDayToFirestore(mealDate));

      return true;
    } catch (e) {
      state = state.copyWith(error: 'Failed to replace meal: $e');
      return false;
    }
  }

  /// Select a meal version in the current meal plan
  Future<bool> selectMealVersionInPlan({
    required DateTime mealDate,
    required String mealType,
    required GeneratedMeal selectedMeal,
  }) async {
    try {
      final currentPlan = state.currentPlan;
      if (currentPlan == null) return false;

      // Get the active meal version to extract its nutrition information
      final activeMeal = selectedMeal.getActiveMeal();

      // Update the meal with nutrition information from the selected version
      final mealWithUpdatedNutrition = selectedMeal.copyWith(
        nutrition: activeMeal.nutrition,
        name: activeMeal.name,
        description: activeMeal.description,
        ingredients: activeMeal.ingredients,
        preparationTime: activeMeal.preparationTime,
        difficulty: activeMeal.difficulty,
      );

      // Find the day and meal to update selection
      final updatedDays = currentPlan.mealPlan.days.map((day) {
        if (_isSameDay(day.date, mealDate)) {
          final updatedMeals = day.meals.map((meal) {
            if (meal.type == mealType) {
              return mealWithUpdatedNutrition;
            }
            return meal;
          }).toList();

          return day.copyWith(meals: updatedMeals);
        }
        return day;
      }).toList();

      // Create updated meal plan
      final updatedMealPlan = currentPlan.mealPlan.copyWith(days: updatedDays);
      final updatedResponse = currentPlan.copyWith(mealPlan: updatedMealPlan);

      // Save to local storage
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.saveCurrentMealPlan(updatedMealPlan.toJson());

      // Update state
      state = state.copyWith(
        currentPlan: updatedResponse,
        lastUpdated: DateTime.now(),
      );

      // Sync with meal plan generation provider for UI consistency
      _syncWithMealPlanGenerationProvider(updatedResponse);

      // Sync specific day to Firestore in background (optimized for meal selection)
      unawaited(_syncSingleDayToFirestore(mealDate));

      return true;
    } catch (e) {
      state = state.copyWith(error: 'Failed to select meal version: $e');
      return false;
    }
  }



  /// Helper method to check if two dates are the same day
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  /// Clear meal replacement loading flags for meals that have been updated
  Future<void> _clearReplacementLoadingFlags(LocalStorageService localStorageService) async {
    try {
      final currentPlan = state.currentPlan;
      if (currentPlan == null) return;

      // Clear loading flags for all meals in the current plan
      for (final day in currentPlan.mealPlan.days) {
        final dateKey = day.date.toIso8601String().split('T')[0];

        for (final meal in day.meals) {
          // Check if this meal has new replacements and clear its loading flag
          if (localStorageService.isMealReplacementLoading(dateKey, meal.type)) {
            await localStorageService.clearMealReplacementLoading(dateKey, meal.type);
            AppLogger.info('CurrentMealPlanNotifier: Cleared replacement loading flag for ${meal.type} on $dateKey');
          }
        }
      }
    } catch (e) {
      AppLogger.error('CurrentMealPlanNotifier: Error clearing replacement loading flags: $e');
    }
  }

  /// Check if a meal replacement is currently loading
  bool isMealReplacementLoading(DateTime mealDate, String mealType) {
    try {
      // This will be used by the UI to show loading states
      // The actual check is done via local storage in the UI components
      return false; // Placeholder - actual implementation in UI
    } catch (e) {
      return false;
    }
  }

  /// Check if local storage has meal plan data
  /// Used to determine if Firestore sync is needed on app launch
  Future<bool> hasLocalMealPlanData() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final mealPlanData = localStorageService.loadCurrentMealPlan();
      return mealPlanData != null;
    } catch (e) {
      AppLogger.error('CurrentMealPlanNotifier: Error checking local meal plan data: $e');
      return false;
    }
  }

  /// Set refreshing state for pull-to-refresh functionality
  void setRefreshing(bool isRefreshing) {
    state = state.copyWith(isRefreshing: isRefreshing);
  }

  /// Public method to reload meal plan from local storage
  Future<void> loadFromLocalStorage() async {
    await _loadFromLocalStorage();
  }

  /// Clear meal generation error from local storage and state
  Future<void> clearMealGenerationError() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.clearMealGenerationError();

      // Clear error from state and reload
      state = state.copyWith(error: null);
      await _loadFromLocalStorage();
    } catch (e) {
      AppLogger.error('CurrentMealPlanNotifier: Error clearing meal generation error: $e');
    }
  }

  /// Explicitly sync meal plan from Firestore to local storage
  /// This can be called from UI components to ensure latest data is loaded
  Future<bool> syncFromFirestore() async {
    try {
      final mealPlanService = await ref.read(mealPlanServiceProvider.future);
      final success = await mealPlanService.syncMealPlanFromFirestore();

      if (success) {
        // Reload from local storage to update UI with synced data
        await _loadFromLocalStorage();
        return true;
      }
      return false;
    } catch (e) {
      state = state.copyWith(error: 'Failed to sync from Firestore: $e');
      return false;
    }
  }

  /// Sync meal plan to Firestore in background
  Future<void> _syncToFirestore(GeneratedMealPlan mealPlan) async {
    try {
      final mealPlanService = await ref.read(mealPlanServiceProvider.future);
      await mealPlanService.syncCurrentMealPlanToFirestore();
    } catch (e) {
      // Log error but don't update state as this is a background operation
      AppLogger.error('CurrentMealPlanNotifier: Failed to sync meal plan to Firestore: $e');
    }
  }

  /// Sync a specific day to Firestore (optimized for individual meal updates)
  Future<void> _syncSingleDayToFirestore(DateTime dayDate) async {
    try {
      final mealPlanService = await ref.read(mealPlanServiceProvider.future);
      await mealPlanService.syncCurrentMealPlanToFirestore();
    } catch (e) {
      // Log error but don't update state as this is a background operation
      AppLogger.error('CurrentMealPlanNotifier: Failed to sync single day to Firestore: $e');
    }
  }

  /// Sync current meal plan with the meal plan generation provider for UI consistency
  /// This method ensures that meal nutrition information reflects the selected meal versions
  void _syncWithMealPlanGenerationProvider(MealPlanResponse updatedPlan) {
    try {
      // Process the meal plan to ensure nutrition information matches selected meal versions
      final processedDays = updatedPlan.mealPlan.days.map((day) {
        final processedMeals = day.meals.map((meal) {
          // If a meal has a selected version, ensure its nutrition matches the active meal
          if (meal.selectedMealId != null) {
            final activeMeal = meal.getActiveMeal();
            return meal.copyWith(
              nutrition: activeMeal.nutrition,
              name: activeMeal.name,
              description: activeMeal.description,
              ingredients: activeMeal.ingredients,
              preparationTime: activeMeal.preparationTime,
              difficulty: activeMeal.difficulty,
            );
          }
          return meal;
        }).toList();

        return day.copyWith(meals: processedMeals);
      }).toList();

      final processedPlan = updatedPlan.copyWith(
        mealPlan: updatedPlan.mealPlan.copyWith(days: processedDays),
      );

      final mealPlanGenerationNotifier = ref.read(mealPlanGenerationNotifierProvider.notifier);
      mealPlanGenerationNotifier.updateGeneratedPlan(processedPlan);
    } catch (e) {
      // Log error but don't fail the operation
      AppLogger.error('CurrentMealPlanNotifier: Failed to sync with meal plan generation provider: $e');
    }
  }
}

/// Provider for today's meals
@riverpod
List<GeneratedMeal> todaysMeals(Ref ref) {
  ref.watch(currentMealPlanNotifierProvider);
  final notifier = ref.read(currentMealPlanNotifierProvider.notifier);
  return notifier.getTodaysMeals();
}

/// Provider for specific meal type today
@riverpod
GeneratedMeal? todaysMealByType(Ref ref, String mealType) {
  ref.watch(currentMealPlanNotifierProvider);
  final notifier = ref.read(currentMealPlanNotifierProvider.notifier);
  return notifier.getTodaysMealByType(mealType);
}
