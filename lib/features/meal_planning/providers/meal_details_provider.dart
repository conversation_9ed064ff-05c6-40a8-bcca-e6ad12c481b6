import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../data/models/meal_plan_request.dart';
import '../providers/current_meal_plan_provider.dart';

part 'meal_details_provider.freezed.dart';
part 'meal_details_provider.g.dart';

@freezed
class MealDetailsState with _$MealDetailsState {
  const factory MealDetailsState({
    @Default(false) bool isReplacementLoading,
    @Default(false) bool showGenerationView,
    @Default({}) Set<String> expandedIngredients,
    String? error,
    StreamSubscription<DocumentSnapshot>? firestoreSubscription,
    @Default(true) bool isActive, // Track if the provider is active
  }) = _MealDetailsState;
}

@riverpod
class MealDetailsNotifier extends _$MealDetailsNotifier {
  @override
  MealDetailsState build() {
    return const MealDetailsState();
  }

  /// Initialize replacement loading state on page load
  void initializeReplacementLoadingState() {
    try {
      // This method can be used to check for any ongoing replacement operations
      // and set the appropriate loading state
      state = state.copyWith(isReplacementLoading: false);
    } catch (e) {
      debugPrint('Error initializing replacement loading state: $e');
      // Don't rethrow to avoid crashing the app
    }
  }

  /// Deactivate the provider to prevent state updates
  void deactivate() {
    try {
      // Cancel any active subscriptions
      state.firestoreSubscription?.cancel();
      state = state.copyWith(isActive: false, firestoreSubscription: null);
    } catch (e) {
      debugPrint('Error deactivating meal details provider: $e');
    }
  }

  /// Set replacement loading state
  void setReplacementLoading(bool isLoading) {
    try {
      if (state.isActive) {
        state = state.copyWith(isReplacementLoading: isLoading);
      }
    } catch (e) {
      debugPrint('Error setting replacement loading state: $e');
      // Don't rethrow to avoid crashing the app
    }
  }

  /// Toggle generation view state for bottom sheet
  void setShowGenerationView(bool show) {
    try {
      if (state.isActive) {
        state = state.copyWith(showGenerationView: show);
      }
    } catch (e) {
      debugPrint('Error setting generation view state: $e');
    }
  }

  /// Toggle ingredient expansion state
  void toggleIngredientExpansion(String ingredientName) {
    try {
      if (state.isActive) {
        final expandedIngredients = Set<String>.from(state.expandedIngredients);
        if (expandedIngredients.contains(ingredientName)) {
          expandedIngredients.remove(ingredientName);
        } else {
          expandedIngredients.add(ingredientName);
        }
        state = state.copyWith(expandedIngredients: expandedIngredients);
      }
    } catch (e) {
      debugPrint('Error toggling ingredient expansion: $e');
    }
  }

  /// Set error message
  void setError(String? error) {
    try {
      if (state.isActive) {
        state = state.copyWith(error: error);
      }
    } catch (e) {
      debugPrint('Error setting error message: $e');
    }
  }

  /// Clear error message
  void clearError() {
    try {
      if (state.isActive) {
        state = state.copyWith(error: null);
      }
    } catch (e) {
      debugPrint('Error clearing error message: $e');
    }
  }

  /// Set Firestore subscription for replacement updates
  void setFirestoreSubscription(StreamSubscription<DocumentSnapshot>? subscription) {
    try {
      if (state.isActive) {
        // Cancel existing subscription if any
        state.firestoreSubscription?.cancel();
        state = state.copyWith(firestoreSubscription: subscription);
      } else {
        // If not active, just cancel the subscription
        subscription?.cancel();
      }
    } catch (e) {
      debugPrint('Error setting Firestore subscription: $e');
      // Still try to cancel the subscription to prevent memory leaks
      subscription?.cancel();
    }
  }

  /// Get the display meal from current meal plan state
  GeneratedMeal getDisplayMeal(
    CurrentMealPlanState currentMealPlan,
    GeneratedMeal originalMeal,
    DayMealPlan dayMealPlan,
  ) {
    // Try to get the meal from the provider first (for real-time updates)
    final dayPlan = currentMealPlan.currentPlan?.mealPlan.days.firstWhere(
      (DayMealPlan day) => day.date.day == dayMealPlan.date.day &&
               day.date.month == dayMealPlan.date.month &&
               day.date.year == dayMealPlan.date.year,
      orElse: () => dayMealPlan,
    );

    GeneratedMeal? providerMeal;

    if (dayPlan != null) {
      // First try to match by meal ID if available
      if (originalMeal.id != null) {
        providerMeal = dayPlan.meals.where((meal) => meal.id == originalMeal.id).firstOrNull;
      }

      // If no ID match found, fall back to matching by type and position
      if (providerMeal == null) {
        // Find meals of the same type
        final mealsOfSameType = dayPlan.meals.where((meal) => meal.type == originalMeal.type).toList();

        // If there's only one meal of this type, use it
        if (mealsOfSameType.length == 1) {
          providerMeal = mealsOfSameType.first;
        } else if (mealsOfSameType.isNotEmpty) {
          // If multiple meals of same type, try to match by name or use first one
          providerMeal = mealsOfSameType.where((meal) => meal.name == originalMeal.name).firstOrNull ??
                        mealsOfSameType.first;
        }
      }
    }

    // Use provider meal if available (ensures real-time sync), otherwise fall back to original
    final currentMeal = providerMeal ?? originalMeal;

    // Get active meal for other properties
    final activeMeal = currentMeal.getActiveMeal();

    // If we have a provider meal with updated consumption state, override the active meal's consumption state
    final displayMeal = providerMeal != null
        ? activeMeal.copyWith(
            isConsumed: providerMeal.isConsumed,
            consumedAt: providerMeal.consumedAt,
          )
        : activeMeal;

    return displayMeal;
  }

  /// Get the root meal (this meal is always the root in the new structure)
  GeneratedMeal getRootMeal(GeneratedMeal meal) {
    // In the new structure, the meal passed to this provider is always the root meal
    return meal;
  }

  /// Handle meal version selection
  Future<void> handleMealVersionSelection(
    MealVersion version,
    GeneratedMeal currentMeal,
    DateTime mealDate,
    String mealType,
  ) async {
    try {
      clearError();

      // Copy only non-root fields from the selected meal version to the root meal
      // Preserve root-only fields: isConsumed, consumedAt, type, replacementHistory, replacementCount
      final selectedMeal = version.meal;
      final updatedMeal = currentMeal.copyWith(
        name: selectedMeal.name,
        ingredients: selectedMeal.ingredients,
        nutrition: selectedMeal.nutrition,
        description: selectedMeal.description,
        preparationTime: selectedMeal.preparationTime,
        difficulty: selectedMeal.difficulty,
        selectedMealId: version.id,
        // Note: type is preserved from root meal (currentMeal.type), not copied from replacement
        // Note: isConsumed and consumedAt are preserved from root meal
        // Note: replacementHistory and replacementCount are preserved from root meal
      );

      await ref.read(currentMealPlanNotifierProvider.notifier).replaceMealInPlan(
        mealDate: mealDate,
        mealType: mealType,
        newMeal: updatedMeal,
      );
    } catch (e) {
      setError('خطأ في تحديد نسخة الوجبة: $e');
    }
  }

  /// Handle current meal state change from provider
  void handleCurrentMealStateChange(
    CurrentMealPlanState? previous,
    CurrentMealPlanState next,
    GeneratedMeal originalMeal,
    DayMealPlan dayMealPlan,
  ) {
    // This method can be used to react to meal plan state changes
    // For example, updating loading states or handling replacement completion
    
    // Check if replacement loading should be updated based on state changes
    final displayMeal = getDisplayMeal(next, originalMeal, dayMealPlan);
    
    // You can add logic here to detect when replacements are completed
    // and update the loading state accordingly
  }

  /// Dispose method to clean up resources
  void dispose() {
    // Cancel Firestore subscription if any
    state.firestoreSubscription?.cancel();
  }
}

/// Provider to check if ingredient is expanded
@riverpod
bool isIngredientExpanded(Ref ref, String ingredientName) {
  final mealDetailsState = ref.watch(mealDetailsNotifierProvider);
  return mealDetailsState.expandedIngredients.contains(ingredientName);
}
