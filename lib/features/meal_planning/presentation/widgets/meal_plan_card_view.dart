import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../data/models/meal_plan_request.dart';
import 'initial_generation_card.dart';
import 'day_navigation_header.dart';
import 'nutrition_summary_card.dart';
import 'meal_card_item.dart';
import 'generate_extra_days_card.dart';
import 'action_buttons_section.dart';
import 'error_card_widget.dart';
import 'home_app_bar.dart';

class MealPlanCardView extends ConsumerStatefulWidget {
  final MealPlanResponse? plan;
  final Function(GeneratedMeal meal, DayMealPlan dayMealPlan) onMealTap;
  final String? showError;
  final bool includeAppBar;
  final Future<void> Function()? onRefresh;

  const MealPlanCardView({
    super.key,
    required this.plan,
    required this.onMealTap,
    this.showError,
    this.includeAppBar = false,
    this.onRefresh,
  });

  @override
  ConsumerState<MealPlanCardView> createState() => _MealPlanCardViewState();
}

class _MealPlanCardViewState extends ConsumerState<MealPlanCardView>
    with TickerProviderStateMixin {
  int _selectedDayIndex = 0;
  bool _hasAutoFocused = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Auto-focus on current day after loading
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _autoFocusOnCurrentDay();
      _animationController.forward();
    });
  }

  @override
  void didUpdateWidget(MealPlanCardView oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // If plan changed from null to non-null, auto-focus on current day
    if (oldWidget.plan == null && widget.plan != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _autoFocusOnCurrentDay();
      });
    }
  }

  /// Auto-focus on the current day (only once per session)
  void _autoFocusOnCurrentDay() {
    if (_hasAutoFocused || widget.plan == null) return;

    final plan = widget.plan!;
    if (plan.mealPlan.days.isEmpty) return;

    final currentDayIndex = _getCurrentDayIndex(plan);
    
    if (currentDayIndex != _selectedDayIndex &&
        currentDayIndex < plan.mealPlan.days.length) {
      setState(() {
        _selectedDayIndex = currentDayIndex;
      });
    }

    _hasAutoFocused = true;
  }

  /// Get the index of the current day in the meal plan
  int _getCurrentDayIndex(MealPlanResponse plan) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    for (int i = 0; i < plan.mealPlan.days.length; i++) {
      final day = plan.mealPlan.days[i];
      final dayDateOnly = DateTime(day.date.year, day.date.month, day.date.day);

      if (dayDateOnly.isAtSameMomentAs(today)) {
        return i;
      }
    }

    return 0;
  }

  /// Get the index to navigate back to (today if exists, otherwise latest day)
  int _getBackNavigationIndex(MealPlanResponse plan) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // First, try to find today's meal plan
    for (int i = 0; i < plan.mealPlan.days.length; i++) {
      final day = plan.mealPlan.days[i];
      final dayDateOnly = DateTime(day.date.year, day.date.month, day.date.day);

      if (dayDateOnly.isAtSameMomentAs(today)) {
        return i; // Found today, return its index
      }
    }

    // If today is not found, return the latest day (last index)
    return plan.mealPlan.days.length - 1;
  }

  /// Handle day selection change
  void _onDayChanged(int newIndex) {
    if (newIndex != _selectedDayIndex) {
      _animationController.reset();
      setState(() {
        _selectedDayIndex = newIndex;
      });
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Wrap CustomScrollView with RefreshIndicator if onRefresh is provided
  /// Only shows pull-to-refresh on non-web platforms (mobile/desktop)
  /// Web platforms should use the refresh button in the app bar instead
  Widget _wrapWithRefreshIndicator(Widget child) {
    if (widget.onRefresh != null && !kIsWeb) {
      return RefreshIndicator(
        onRefresh: widget.onRefresh!,
        child: child,
      );
    }
    return child;
  }

  @override
  Widget build(BuildContext context) {
    // If no plan exists, show only the generation page
    if (widget.plan == null) {
      return _wrapWithRefreshIndicator(
        CustomScrollView(
          slivers: [
            // SliverAppBar (if requested)
            if (widget.includeAppBar)
              HomeAppBar.buildSliverAppBar(context, ref, onRefresh: widget.onRefresh),

            // Error Display
            if (widget.showError != null)
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: ErrorCardWidget(error: widget.showError!),
                ),
              ),

            // Initial Generation Card
            const SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: InitialGenerationCard(),
              ),
            ),
          ],
        ),
      );
    }

    final plan = widget.plan!;
    final totalDays = plan.mealPlan.days.length;

    // Handle case where selected day is out of bounds
    if (_selectedDayIndex >= totalDays) {
      return _buildGenerateExtraDaysView();
    }

    final selectedDay = plan.mealPlan.days[_selectedDayIndex];

    return _wrapWithRefreshIndicator(
      CustomScrollView(
        slivers: [
          // SliverAppBar (if requested)
          if (widget.includeAppBar)
            HomeAppBar.buildSliverAppBar(context, ref, onRefresh: widget.onRefresh),

          // Error Display
          if (widget.showError != null)
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: ErrorCardWidget(error: widget.showError!),
              ),
            ),

          // Sticky Day Navigation Header
          SliverPersistentHeader(
            pinned: true,
            delegate: StickyDayNavigationHeaderDelegate(
              totalDays: totalDays,
              selectedDayIndex: _selectedDayIndex,
              days: plan.mealPlan.days,
              onDayChanged: _onDayChanged,
              onGenerateExtraDays: () => _onDayChanged(totalDays),
            ),
          ),

          // Selected Day Content with Animation
          SliverToBoxAdapter(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Nutrition Summary Card
                    NutritionSummaryCard(
                      day: selectedDay,
                    ),

                    const SizedBox(height: 16),

                    // Meals Cards
                    _buildMealsSection(selectedDay),

                    const SizedBox(height: 16),

                    // Action Buttons (only show when meal plan exists)
                    const ActionButtonsSection(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenerateExtraDaysView() {
    return _wrapWithRefreshIndicator(
      CustomScrollView(
        slivers: [
          // SliverAppBar (if requested)
          if (widget.includeAppBar)
            HomeAppBar.buildSliverAppBar(context, ref, onRefresh: widget.onRefresh),

          // Error Display
          if (widget.showError != null)
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: ErrorCardWidget(error: widget.showError!),
              ),
            ),

          // Sticky Day Navigation Header (showing extra days option)
          SliverPersistentHeader(
            pinned: true,
            delegate: StickyDayNavigationHeaderDelegate(
              totalDays: widget.plan!.mealPlan.days.length,
              selectedDayIndex: _selectedDayIndex,
              days: widget.plan!.mealPlan.days,
              onDayChanged: _onDayChanged,
              onGenerateExtraDays: () {}, // Already on extra days
            ),
          ),

          // Generate Extra Days Card and Action Buttons
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Generate Extra Days Card
                  GenerateExtraDaysCard(
                    onBackPressed: widget.plan != null && widget.plan!.mealPlan.days.isNotEmpty
                        ? () => _onDayChanged(_getBackNavigationIndex(widget.plan!))
                        : null, // Hide back button if no meal plan exists
                  ),

                  const SizedBox(height: 16),

                  // Action Buttons
                  const ActionButtonsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMealsSection(DayMealPlan day) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الوجبات (${day.meals.length})',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
        ),
        const SizedBox(height: 12),

        // Meals list as cards
        ...day.meals.map((meal) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: MealCardItem(
                meal: meal,
                dayMealPlan: day,
                onTap: () => widget.onMealTap(meal, day),
              ),
            )),
      ],
    );
  }
}
