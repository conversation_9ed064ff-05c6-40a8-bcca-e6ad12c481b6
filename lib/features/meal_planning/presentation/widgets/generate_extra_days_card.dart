import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/router/app_routes.dart';
import '../../../../core/services/local_storage_service.dart';
import '../../../../core/theme/app_colors.dart';
import '../../providers/meal_plan_generation_provider.dart';
import '../../providers/current_meal_plan_provider.dart';

class GenerateExtraDaysCard extends ConsumerStatefulWidget {
  final VoidCallback? onBackPressed;

  const GenerateExtraDaysCard({
    super.key,
    this.onBackPressed,
  });

  @override
  ConsumerState<GenerateExtraDaysCard> createState() => _GenerateExtraDaysCardState();
}

class _GenerateExtraDaysCardState extends ConsumerState<GenerateExtraDaysCard> {
  bool _isGeneratingExtraDays = false;
  bool _isBackgroundLoading = false;

  @override
  void initState() {
    super.initState();
    _checkBackgroundLoading();
  }

  /// Check if meal generation is currently running in background
  Future<void> _checkBackgroundLoading() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final isLoading = localStorageService.isMealGenerationLoading();

      if (mounted) {
        setState(() {
          _isBackgroundLoading = isLoading;
        });
      }
    } catch (e) {
      // Ignore errors in checking background loading
    }
  }

  /// Check if there are too many future days (3+ days ahead of current date)
  bool _checkTooManyFutureDays(CurrentMealPlanState mealPlanState) {
    final currentPlan = mealPlanState.currentPlan;
    final mealPlan = currentPlan?.mealPlan;
    if (currentPlan == null || mealPlan == null || mealPlan.days.isEmpty) {
      return false; // No days exist, can generate
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // Find the latest day in the meal plan
    final days = mealPlan.days;
    final latestDay = days.reduce((a, b) => a.date.isAfter(b.date) ? a : b);
    final latestDate = DateTime(latestDay.date.year, latestDay.date.month, latestDay.date.day);

    // Check if the latest day is 3 or more days in the future
    final daysDifference = latestDate.difference(today).inDays;
    return daysDifference >= 3;
  }

  @override
  Widget build(BuildContext context) {
    // Watch for meal plan state changes (including errors)
    final mealPlanState = ref.watch(currentMealPlanNotifierProvider);
    final hasError = mealPlanState.error != null;

    // Check if we have too many future days already
    final hasTooManyFutureDays = _checkTooManyFutureDays(mealPlanState);

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.add_circle_outline,
                size: 40,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 24),

            // Title
            Text(
              'إضافة أيام جديدة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Description
            Text(
              'هل تريد إضافة 3 أيام إضافية لخطة الوجبات الخاصة بك؟\nسيتم إنشاء وجبات جديدة ومتنوعة لك.',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textSecondary,
                    height: 1.5,
                  ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),

            // Error Display
            if (hasError) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.red.shade600,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'خطأ في إنشاء الوجبات',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.red.shade700,
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      mealPlanState.error!,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.red.shade700,
                            height: 1.4,
                          ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: () async {
                          // Clear the error
                          await ref.read(currentMealPlanNotifierProvider.notifier).clearMealGenerationError();
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red.shade600,
                          side: BorderSide(color: Colors.red.shade300),
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        icon: Icon(Icons.refresh, size: 16),
                        label: Text('إعادة المحاولة'),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Future Days Warning
            if (hasTooManyFutureDays) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.schedule,
                          color: Colors.orange.shade600,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'لا يمكن إضافة المزيد من الأيام',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange.shade700,
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لديك بالفعل وجبات مخططة لأكثر من 3 أيام في المستقبل. يُنصح بتناول الوجبات الحالية أولاً قبل إضافة المزيد من الأيام لضمان جودة وطازجية الخطة الغذائية.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.orange.shade700,
                            height: 1.4,
                          ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Generate Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: (_isGeneratingExtraDays || _isBackgroundLoading || hasError || hasTooManyFutureDays)
                    ? null
                    : _showConfirmationBottomSheet,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: (_isGeneratingExtraDays || _isBackgroundLoading)
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : hasError
                        ? Icon(Icons.error_outline)
                        : hasTooManyFutureDays
                            ? Icon(Icons.schedule)
                            : Icon(Icons.restaurant_menu),
                label: Text(
                  (_isGeneratingExtraDays || _isBackgroundLoading)
                      ? _isBackgroundLoading
                          ? 'جاري الإنشاء في الخلفية...'
                          : 'جاري الإنشاء...'
                      : hasError
                          ? 'يرجى إصلاح الخطأ أولاً'
                          : hasTooManyFutureDays
                              ? 'لديك وجبات كافية للأيام القادمة'
                              : 'إنشاء 3 أيام إضافية',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Cancel/Back Button
            if (widget.onBackPressed != null)
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _isGeneratingExtraDays
                      ? null
                      : widget.onBackPressed,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.textSecondary,
                    side: BorderSide(color: AppColors.textSecondary),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  icon: Icon(Icons.arrow_back),
                  label: Text('العودة للخطة الحالية'),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Show confirmation bottom sheet before generating extra days
  void _showConfirmationBottomSheet() {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _GenerateExtraDaysConfirmationBottomSheet(
        onConfirm: _generateExtraDays,
      ),
    );
  }

  /// Generate extra 3 days for the meal plan
  Future<void> _generateExtraDays() async {
    // Close the bottom sheet first
    Navigator.of(context).pop();

    setState(() {
      _isGeneratingExtraDays = true;
    });

    try {
      final notifier = ref.read(mealPlanGenerationNotifierProvider.notifier);
      await notifier.generateExtraDays();

      // Check if background loading started
      await _checkBackgroundLoading();

      // Show appropriate message based on whether background processing started
      if (mounted) {
        if (_isBackgroundLoading) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم بدء إنشاء الوجبات الإضافية في الخلفية، سيتم تحديث الخطة تلقائياً عند الانتهاء'),
              backgroundColor: Colors.blue.shade600,
              duration: Duration(seconds: 4),
            ),
          );
        } else {
          // throw exception to test error handling
          throw Exception('Failed to generate extra days');
          // ScaffoldMessenger.of(context).showSnackBar(
          //   SnackBar(
          //     content: Text('تم إنشاء 3 أيام إضافية بنجاح!'),
          //     backgroundColor: Colors.green.shade600,
          //   ),
          // );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إنشاء أيام إضافية: $e'),
            backgroundColor: Colors.red.shade600,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGeneratingExtraDays = false;
        });
      }
    }
  }
}

/// Bottom sheet widget for confirming extra days generation
class _GenerateExtraDaysConfirmationBottomSheet extends ConsumerWidget {
  final VoidCallback onConfirm;

  const _GenerateExtraDaysConfirmationBottomSheet({
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      gradient: AppColors.primaryGradient,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.restaurant_menu,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'إنشاء 3 أيام إضافية',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(Icons.close),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Settings update option
              _buildSettingsUpdateSection(context),

              const SizedBox(height: 24),

              // Confirmation section
              _buildConfirmationSection(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsUpdateSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.blue.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'معلومات مهمة',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'سيتم إنشاء الوجبات الإضافية بناءً على تفضيلاتك المحفوظة (الوزن، السعرات اليومية، مستوى النشاط، القيود الغذائية، والهدف الصحي).',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.blue.shade700,
                  height: 1.4,
                ),
          ),
          const SizedBox(height: 12),
          Text(
            'إذا كنت تريد تحديث إعدادات خطة الوجبات (الوزن، السعرات، التفضيلات الغذائية) قبل إنشاء الوجبات الجديدة، يمكنك الضغط على الزر أدناه:',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.blue.shade700,
                  height: 1.4,
                ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                context.push(AppRoutes.settings);
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.blue.shade600,
                side: BorderSide(color: Colors.blue.shade300),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              icon: Icon(Icons.edit, size: 18),
              label: Text('تحديث إعدادات الخطة'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmationSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.check_circle_outline,
                color: Colors.green.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'تأكيد الإنشاء',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade700,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'قد تستغرق عملية إنشاء الوجبات بعض الوقت. سيتم إشعارك عند الانتهاء وستظهر الأيام الجديدة تلقائياً.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.green.shade700,
                ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: onConfirm,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              icon: Icon(Icons.check, size: 18),
              label: Text(
                'أوافق - إنشاء 3 أيام إضافية',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }


}
