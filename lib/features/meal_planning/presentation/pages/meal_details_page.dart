
import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/services/local_storage_service.dart';
import '../../data/models/meal_plan_request.dart';
import '../widgets/meal_details/meal_replacement_bottom_sheet.dart';

import '../../providers/current_meal_plan_provider.dart';
import '../../providers/meal_details_provider.dart';
import '../widgets/meal_details/meal_details_header.dart';
import '../widgets/meal_details/meal_image_widget.dart';
import '../widgets/meal_details/meal_ingredients_list.dart';
import '../widgets/meal_details/meal_nutrition_info.dart';

import '../widgets/meal_details/meal_consumption_button.dart';

class MealDetailsPage extends ConsumerStatefulWidget {
  final GeneratedMeal meal;
  final DayMealPlan dayMealPlan;
  final String? date;
  final String? mealType;
  final int? mealSlot;
  final bool openReplacementModal;

  const MealDetailsPage({
    super.key,
    required this.meal,
    required this.dayMealPlan,
    required this.date,
    required this.mealType,
    required this.mealSlot,
    this.openReplacementModal = false,
  });

  @override
  ConsumerState<MealDetailsPage> createState() => _MealDetailsPageState();
}

class _MealDetailsPageState extends ConsumerState<MealDetailsPage> {
  StreamSubscription<DocumentSnapshot>? _replacementTrackerSubscription;

  @override
  void initState() {
    super.initState();
    // Initialize meal details provider state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      _initializeReplacementLoadingState();

      // Start listening for replacement tracker updates
      _startReplacementTrackerListener();

      // Auto-open replacement modal if requested (from notification)
      if (widget.openReplacementModal && mounted) {
        _showMealSelectionBottomSheet();
      }
    });
  }

  @override
  void dispose() {
    // Cancel replacement tracker subscription
    _replacementTrackerSubscription?.cancel();

    // Deactivate the meal details provider to prevent state updates after disposal
    try {
      ref.read(mealDetailsNotifierProvider.notifier).deactivate();
    } catch (e) {
      debugPrint('Error deactivating meal details provider: $e');
    }

    super.dispose();
  }

  /// Initialize replacement loading state by checking local storage flag
  void _initializeReplacementLoadingState() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      if (!mounted) return;

      final mealDate = widget.dayMealPlan.date.toIso8601String().split('T')[0];
      final mealId = widget.meal.id ?? '${widget.meal.type}_${widget.dayMealPlan.date.millisecondsSinceEpoch}';

      final loadingData = localStorageService.loadMealReplacementLoading(mealDate, mealId);

      if (loadingData == null) {
        // No loading flag, set loading to false
        _safelyUpdateProviderState(() {
          ref.read(mealDetailsNotifierProvider.notifier).setReplacementLoading(false);
        });
        return;
      }

      final flagTimestamp = loadingData['timestamp'] as int?;
      if (flagTimestamp == null) {
        // Invalid loading data, clear it and set loading to false
        await localStorageService.clearMealReplacementLoading(mealDate, mealId);
        _safelyUpdateProviderState(() {
          ref.read(mealDetailsNotifierProvider.notifier).setReplacementLoading(false);
        });
        return;
      }

      final flagDatetime = DateTime.fromMillisecondsSinceEpoch(flagTimestamp);

      // Check if meal object contains replacement with datetime larger than flag datetime
      final latestReplacementDatetime = _getLatestReplacementDatetime();

      if (latestReplacementDatetime != null && latestReplacementDatetime.isAfter(flagDatetime)) {
        // Replacement result has come, remove flag and set loading to false
        await localStorageService.clearMealReplacementLoading(mealDate, mealId);
        _safelyUpdateProviderState(() {
          ref.read(mealDetailsNotifierProvider.notifier).setReplacementLoading(false);
        });
      } else {
        // Flag datetime is larger than latest replacement, keep loading
        _safelyUpdateProviderState(() {
          ref.read(mealDetailsNotifierProvider.notifier).setReplacementLoading(true);
        });
      }
    } catch (e) {
      debugPrint('Error initializing replacement loading state: $e');
      _safelyUpdateProviderState(() {
        ref.read(mealDetailsNotifierProvider.notifier).setReplacementLoading(false);
      });
    }
  }

  /// Get the latest replacement datetime from the meal object
  DateTime? _getLatestReplacementDatetime() {
    if (widget.meal.replacementHistory.isEmpty) return null;

    // Find the latest replacement datetime
    DateTime? latestDatetime;
    for (final replacement in widget.meal.replacementHistory) {
      if (latestDatetime == null || replacement.replacedAt.isAfter(latestDatetime)) {
        latestDatetime = replacement.replacedAt;
      }
    }

    return latestDatetime;
  }

  /// Start listening for replacement tracker updates
  void _startReplacementTrackerListener() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    try {
      // Listen to the specific meal's replacement tracker document
      final mealId = widget.meal.id ?? '${widget.meal.type}_${widget.dayMealPlan.date.millisecondsSinceEpoch}';
      _replacementTrackerSubscription = FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('replacement_trackers')
          .doc(mealId)
          .snapshots()
          .listen(
        (snapshot) {
          _handleReplacementTrackerUpdate(snapshot);
        },
        onError: (error) {
          debugPrint('Error listening to replacement tracker: $error');
        },
      );
    } catch (e) {
      debugPrint('Error setting up replacement tracker listener: $e');
    }
  }

  /// Handle replacement tracker document updates
  void _handleReplacementTrackerUpdate(DocumentSnapshot snapshot) async {
    if (!mounted || !snapshot.exists) return;

    try {
      final data = snapshot.data() as Map<String, dynamic>?;
      if (data == null) return;

      final trackerDatetime = (data['datetime'] as Timestamp?)?.toDate();
      final replacementId = data['id'] as String?;

      if (trackerDatetime == null || replacementId == null) return;

      // Check if we have a loading flag for this meal
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      if (!mounted) return;

      final mealDate = widget.dayMealPlan.date.toIso8601String().split('T')[0];
      final mealId = widget.meal.id ?? '${widget.meal.type}_${widget.dayMealPlan.date.millisecondsSinceEpoch}';
      final loadingData = localStorageService.loadMealReplacementLoading(mealDate, mealId);

      if (loadingData == null) return;

      final flagTimestamp = loadingData['timestamp'] as int?;
      if (flagTimestamp == null) return;

      final flagDatetime = DateTime.fromMillisecondsSinceEpoch(flagTimestamp);

      // Check if the tracker datetime is greater than the loading flag datetime
      if (trackerDatetime.isAfter(flagDatetime)) {
        // Clear the loading flag
        await localStorageService.clearMealReplacementLoading(mealDate, mealId);

        // Fetch updated meal data from Firestore and update local storage
        await _fetchAndUpdateMealData();

        // Update UI loading state
        _safelyUpdateProviderState(() {
          ref.read(mealDetailsNotifierProvider.notifier).setReplacementLoading(false);
        });

        // Auto-open replacement bottom sheet to show new options
        // Add a small delay to ensure the UI state is properly updated
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            _showMealSelectionBottomSheet();
          }
        });
      }
    } catch (e) {
      debugPrint('Error handling replacement tracker update: $e');
    }
  }

  /// Fetch updated meal data from Firestore and update local storage
  Future<void> _fetchAndUpdateMealData() async {
    try {
      final currentMealPlanNotifier = ref.read(currentMealPlanNotifierProvider.notifier);

      // Sync from Firestore to get the latest meal data
      await currentMealPlanNotifier.syncFromFirestore();
    } catch (e) {
      debugPrint('Error fetching and updating meal data: $e');
    }
  }

  /// Check if the meal was updated and clear loading state if needed
  void _checkForMealUpdatesAndClearLoading(CurrentMealPlanState currentMealPlan) async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final mealDate = widget.dayMealPlan.date.toIso8601String().split('T')[0];
      final mealId = widget.meal.id ?? '${widget.meal.type}_${widget.dayMealPlan.date.millisecondsSinceEpoch}';

      final loadingData = localStorageService.loadMealReplacementLoading(mealDate, mealId);
      if (loadingData == null) return;

      final flagTimestamp = loadingData['timestamp'] as int?;
      if (flagTimestamp == null) return;

      final flagDatetime = DateTime.fromMillisecondsSinceEpoch(flagTimestamp);

      // Get the updated meal from the current meal plan
      final updatedMeal = _getDisplayMeal(currentMealPlan);

      // Check if the meal has new replacements after the loading flag
      final latestReplacementDatetime = _getLatestReplacementDatetimeFromMeal(updatedMeal);

      if (latestReplacementDatetime != null && latestReplacementDatetime.isAfter(flagDatetime)) {
        // Clear the loading flag and update UI
        await localStorageService.clearMealReplacementLoading(mealDate, mealId);
        _safelyUpdateProviderState(() {
          ref.read(mealDetailsNotifierProvider.notifier).setReplacementLoading(false);
        });

        debugPrint('Meal replacement completed, loading state cleared');
      }
    } catch (e) {
      debugPrint('Error checking for meal updates: $e');
    }
  }

  /// Get the latest replacement datetime from a specific meal object
  DateTime? _getLatestReplacementDatetimeFromMeal(GeneratedMeal meal) {
    if (meal.replacementHistory.isEmpty) return null;

    // Find the latest replacement datetime
    DateTime? latestDatetime;
    for (final replacement in meal.replacementHistory) {
      if (latestDatetime == null || replacement.replacedAt.isAfter(latestDatetime)) {
        latestDatetime = replacement.replacedAt;
      }
    }

    return latestDatetime;
  }

  /// Get the updated day meal plan from current meal plan state
  DayMealPlan _getUpdatedDayMealPlan(CurrentMealPlanState currentMealPlan) {
    // Try to get the updated day plan from the provider
    final updatedDayPlan = currentMealPlan.currentPlan?.mealPlan.days.firstWhere(
      (DayMealPlan day) => day.date.day == widget.dayMealPlan.date.day &&
               day.date.month == widget.dayMealPlan.date.month &&
               day.date.year == widget.dayMealPlan.date.year,
      orElse: () => widget.dayMealPlan,
    );

    return updatedDayPlan ?? widget.dayMealPlan;
  }

  /// Get the display meal using the meal details provider
  GeneratedMeal _getDisplayMeal(CurrentMealPlanState currentMealPlan) {
    final updatedDayMealPlan = _getUpdatedDayMealPlan(currentMealPlan);

    return ref.read(mealDetailsNotifierProvider.notifier).getDisplayMeal(
      currentMealPlan,
      widget.meal,
      updatedDayMealPlan,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Watch the providers to ensure UI rebuilds when state changes
    final currentMealPlan = ref.watch(currentMealPlanNotifierProvider);
    final mealDetailsState = ref.watch(mealDetailsNotifierProvider);
    final displayMeal = _getDisplayMeal(currentMealPlan);
    final updatedDayMealPlan = _getUpdatedDayMealPlan(currentMealPlan);

    // Listen to provider state changes
    ref.listen<CurrentMealPlanState>(currentMealPlanNotifierProvider, (previous, next) {
      if (mounted) {
        final updatedDayMealPlan = _getUpdatedDayMealPlan(next);

        ref.read(mealDetailsNotifierProvider.notifier).handleCurrentMealStateChange(
          previous,
          next,
          widget.meal,
          updatedDayMealPlan,
        );

        // Check if this meal was updated and clear loading state if needed
        _checkForMealUpdatesAndClearLoading(next);
      }
    });

    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          // Header
          MealDetailsHeader(
            meal: displayMeal,
            onClose: () => Navigator.of(context).pop(),
            onChangePressed: _handleChangePressed,
            isReplacementLoading: mealDetailsState.isReplacementLoading,
          ),

          // Scrollable Content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.zero,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Meal Image (now scrollable)
                  MealImageWidget(
                    meal: displayMeal,
                    dayDocumentId: updatedDayMealPlan.dayDocumentId ?? '',
                    mealDate: updatedDayMealPlan.date,
                  ),

                  // Ingredients List
                  Padding(
                    padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                    child: MealIngredientsList(meal: displayMeal),
                  ),
                  const SizedBox(height: 20),

                  // Nutrition Information
                  Padding(
                    padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                    child: MealNutritionInfo(
                      meal: displayMeal,
                      totalDayNutrition: updatedDayMealPlan.totalNutrition,
                    ),
                  ),

                  // Extra padding at bottom to ensure content doesn't get hidden behind fixed sections
                  const SizedBox(height: 100),
                ],
              ),
            ),
          ),

          // Fixed Mark as Taken Button at Bottom
          MealConsumptionButton(
            meal: displayMeal,
            mealDate: updatedDayMealPlan.date,
          ),
        ],
      ),
    );
  }



  /// Handle change button press - show toast if consumed, otherwise show bottom sheet
  void _handleChangePressed() {
    final currentMealPlan = ref.read(currentMealPlanNotifierProvider);
    final displayMeal = _getDisplayMeal(currentMealPlan);

    if (displayMeal.isConsumed) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن تغيير وجبة تم تناولها'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    _showMealSelectionBottomSheet();
  }

  /// Safely update provider state with error handling
  void _safelyUpdateProviderState(void Function() updateFunction) {
    if (!mounted) {
      debugPrint('Widget not mounted, skipping provider state update');
      return;
    }

    try {
      updateFunction();
    } catch (e) {
      debugPrint('Error updating provider state: $e');
      // Don't rethrow to avoid crashing the app
    }
  }

  /// Show meal selection bottom sheet with replacement options
  void _showMealSelectionBottomSheet() {
    // Check if widget is still mounted before accessing providers
    if (!mounted) return;

    try {
      // Get the current data before showing the bottom sheet
      final currentMealPlan = ref.read(currentMealPlanNotifierProvider);
      final updatedDayMealPlan = _getUpdatedDayMealPlan(currentMealPlan);
      final updatedMeal = _getDisplayMeal(currentMealPlan);

      showModalBottomSheet<void>(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return MealReplacementBottomSheet(
            meal: updatedMeal,
            dayMealPlan: updatedDayMealPlan,
          );
        },
      );
    } catch (e) {
      debugPrint('Error showing meal selection bottom sheet: $e');
    }
  }
}