const {authenticateUser} = require('../../middleware/auth');
const {with<PERSON><PERSON>po<PERSON><PERSON><PERSON><PERSON>, createApiError, createSuccessResponse} = require('../../middleware/responseHandler');
const {mealReplacementSchema} = require('../../utils/validators');
const {processReplacementInBackground} = require('../../services/mealReplacementService');

/**
 * POST /meal-plans/replace-meal
 * Authenticated meal replacement endpoint
 * Generates a new meal replacement and adds it directly to Firestore
 * Returns immediately with success status, processes replacement in background
 */
async function replaceMealHandler(req, res, validatedData) {
  const authenticatedUserId = req.user.uid; // From auth middleware
  const {day_document_id, meal_id, custom_ingredients} = validatedData; // Already validated by middleware

  console.log('Starting meal replacement for user:', authenticatedUserId, {
    dayDocumentId: day_document_id,
    mealId: meal_id,
    hasCustomIngredients: !!custom_ingredients,
  });

  // Start background processing (don't await)
  processBackgroundMealReplacement(authenticatedUserId, day_document_id, meal_id, custom_ingredients);

  // Return success response (handled by response middleware)
  return createSuccessResponse(
    null,
    'تم بدء استبدال الوجبة في الخلفية'
  );
}

/**
 * Process meal replacement in background
 * @param {string} userId - User ID
 * @param {string} dayDocumentId - Day document ID
 * @param {string} mealId - Meal ID to replace
 * @param {string} customIngredients - Optional custom ingredients
 */
async function processBackgroundMealReplacement(userId, dayDocumentId, mealId, customIngredients) {
  try {
    console.log('Processing meal replacement in background for user:', userId, {
      dayDocumentId,
      mealId,
    });

    // Use the service function to handle the replacement
    await processReplacementInBackground(userId, dayDocumentId, mealId, customIngredients);

    console.log('Background meal replacement completed successfully for user:', userId);


  } catch (error) {
    console.error('Background processing: Error replacing meal for user:', userId, error);
    // Background errors are logged but don't affect the client response
    // since the client already received a success response
  }
}

module.exports = {
  method: 'post',
  path: '/meal-plans/replace-meal',
  middleware: [authenticateUser],
  handler: withResponseHandler(replaceMealHandler, mealReplacementSchema),
};
