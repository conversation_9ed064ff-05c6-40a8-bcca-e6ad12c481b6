const {authenticateUser} = require('../../middleware/auth');
const {with<PERSON><PERSON>po<PERSON><PERSON><PERSON><PERSON>, createApiError, createSuccessResponse} = require('../../middleware/responseHandler');
const {mealImageGenerationSchema} = require('../../utils/validators');
const {processImageGenerationInBackground, getMealFromFirestore} = require('../../services/mealImageService');

/**
 * POST /meal-plans/generate-image
 * Authenticated meal image generation endpoint
 * Generates an image for a specific meal using OpenAI DALL-E
 * Returns immediately with success status, processes image generation in background
 */
async function generateImageHandler(req, res, validatedData) {
  const authenticatedUserId = req.user.uid; // From auth middleware
  const {day_document_id, meal_date, meal_id} = validatedData; // Already validated by middleware

  // Validate that either day_document_id or meal_date is provided
  if (!day_document_id && !meal_date) {
    throw createApiError('Either day_document_id or meal_date is required', 400, 'VALIDATION_ERROR');
  }

  console.log('Starting meal image generation for user:', authenticatedUserId, {
    dayDocumentId: day_document_id,
    mealDate: meal_date,
    mealId: meal_id,
  });

  // Validate that the meal exists before starting background processing
  let mealData;
  if (day_document_id) {
    mealData = await getMealFromFirestore(authenticatedUserId, day_document_id, meal_id);
  } else if (meal_date) {
    const {getMealFromFirestoreByDate} = require('../../services/mealImageService');
    const result = await getMealFromFirestoreByDate(authenticatedUserId, meal_date, meal_id);
    mealData = result?.meal;
  }

  if (!mealData) {
    throw createApiError('Meal not found. Please check the day document ID/meal date and meal ID.', 404, 'MEAL_NOT_FOUND');
  }

  // Check if meal already has an image
  if (mealData.image_url) {
    throw createApiError('This meal already has an image. Image regeneration is not currently supported.', 409, 'IMAGE_ALREADY_EXISTS');
  }

  console.log('Validation successful, starting background image generation for meal:', mealData.name);

  // Start background processing (don't await)
  processBackgroundImageGeneration(authenticatedUserId, day_document_id, meal_id, meal_date);

  // Return success response (handled by response middleware)
  return createSuccessResponse(
    null,
    'تم بدء إنشاء صورة الوجبة في الخلفية'
  );
}

/**
 * Process meal image generation in background
 * @param {string} userId - User ID
 * @param {string} dayDocumentId - Day document ID (optional if mealDate is provided)
 * @param {string} mealId - Meal ID
 * @param {string} mealDate - Meal date in YYYY-MM-DD format (optional if dayDocumentId is provided)
 */
async function processBackgroundImageGeneration(userId, dayDocumentId, mealId, mealDate = null) {
  try {
    console.log('Processing meal image generation in background for user:', userId, {
      dayDocumentId,
      mealId,
    });

    // Use the service function to handle the image generation
    await processImageGenerationInBackground(userId, dayDocumentId, mealId, mealDate);

    console.log('Background meal image generation completed successfully for user:', userId);

  } catch (error) {
    console.error('Background processing: Error generating meal image for user:', userId, error);
    // Background errors are logged but don't affect the client response
    // since the client already received a success response
  }
}

module.exports = {
  method: 'post',
  path: '/meal-plans/generate-image',
  middleware: [authenticateUser],
  handler: withResponseHandler(generateImageHandler, mealImageGenerationSchema),
};
