const {initializeFirebase} = require('../config/firebase');
const {Timestamp} = require('firebase-admin/firestore');

/**
 * Background Job Tracking Service
 * Handles tracking for 4 types of background jobs:
 * - bjt_shopping_list (single document: 'it')
 * - bjt_days_generating (single document: 'it')
 * - bjt_meal_replacement (multi document: key = meal_id)
 * - bjt_meal_image_generating (multi document: key = meal_id)
 */

// Collection names with bjt_ prefix (background-job tracking)
const COLLECTIONS = {
  SHOPPING_LIST: 'bjt_shopping_list',
  DAYS_GENERATING: 'bjt_days_generating',
  MEAL_REPLACEMENT: 'bjt_meal_replacement',
  MEAL_IMAGE_GENERATING: 'bjt_meal_image_generating',
};

// Job statuses
const JOB_STATUS = {
  IN_PROGRESS: 'in-progress',
  SUCCESS: 'success',
  FAIL: 'fail',
};

/**
 * Check if a background job can be started and create/update tracking document
 * @param {string} userId - User ID
 * @param {string} collectionName - Collection name from COLLECTIONS
 * @param {string} documentId - Document ID ('it' for single docs, meal_id for multi docs)
 * @param {Object} meta - Optional metadata (e.g., from_date, to_date, days_count)
 * @param {number} maxAttempts - Maximum allowed attempts (default: 3)
 * @param {string} jobType - Job type for error messages
 * @return {Promise<void>}
 */
async function checkAndStartBackgroundJob(userId, collectionName, documentId, meta = {}, maxAttempts = 3, jobType = 'العملية') {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    const jobRef = db
      .collection('users')
      .doc(userId)
      .collection(collectionName)
      .doc(documentId);

    const currentTime = Timestamp.now();

    // Get existing job document
    const existingJob = await jobRef.get();

    if (existingJob.exists) {
      const jobData = existingJob.data();

      // Check if job is currently in progress
      if (jobData.status === JOB_STATUS.IN_PROGRESS) {
        const error = new Error(`يوجد عملية ${jobType} قيد التنفيذ حالياً. يرجى الانتظار حتى اكتمالها.`);
        error.code = 'BACKGROUND_JOB_IN_PROGRESS';
        error.details = {
          status: jobData.status,
          triggerCount: jobData.trigger_count,
          lastTriggerDatetime: jobData.last_trigger_datetime?.toDate()?.toISOString(),
          jobType,
        };
        throw error;
      }

      // Check attempt limits
      const currentTriggerCount = jobData.trigger_count || 0;
      if (currentTriggerCount >= maxAttempts) {
        const error = new Error(`تم الوصول للحد الأقصى من المحاولات (${maxAttempts}) لهذه ${jobType}.`);
        error.code = 'MAX_ATTEMPTS_EXCEEDED';
        error.details = {
          triggerCount: currentTriggerCount,
          maxAttempts,
          jobType,
        };
        throw error;
      }

      // Update existing document with incremented trigger count
      await jobRef.update({
        trigger_count: currentTriggerCount + 1,
        status: JOB_STATUS.IN_PROGRESS,
        status_message: '',
        last_trigger_datetime: currentTime,
        ...(Object.keys(meta).length > 0 && {meta}),
      });

      console.log(`Background job updated for user: ${userId}, collection: ${collectionName}, doc: ${documentId}, trigger_count: ${currentTriggerCount + 1}`);
    } else {
      // Create new job document
      await jobRef.set({
        trigger_count: 1,
        status: JOB_STATUS.IN_PROGRESS,
        status_message: '',
        last_trigger_datetime: currentTime,
        last_completion_datetime: null,
        ...(Object.keys(meta).length > 0 && {meta}),
      });

      console.log(`Background job created for user: ${userId}, collection: ${collectionName}, doc: ${documentId}, trigger_count: 1`);
    }
  } catch (error) {
    console.error('Error checking/starting background job:', error);
    throw error;
  }
}

/**
 * Complete a background job (success or failure)
 * @param {string} userId - User ID
 * @param {string} collectionName - Collection name from COLLECTIONS
 * @param {string} documentId - Document ID
 * @param {string} status - JOB_STATUS.SUCCESS or JOB_STATUS.FAIL
 * @param {string} statusMessage - Optional status message (required for failures)
 * @return {Promise<void>}
 */
async function completeBackgroundJob(userId, collectionName, documentId, status = JOB_STATUS.SUCCESS, statusMessage = '') {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    const jobRef = db
      .collection('users')
      .doc(userId)
      .collection(collectionName)
      .doc(documentId);

    const currentTime = Timestamp.now();

    // Check if document exists
    const existingJob = await jobRef.get();
    if (!existingJob.exists) {
      console.log(`Background job not found for completion: user: ${userId}, collection: ${collectionName}, doc: ${documentId}`);
      return;
    }

    // Update job status
    await jobRef.update({
      status: status,
      status_message: statusMessage,
      last_completion_datetime: currentTime,
    });

    console.log(`Background job completed for user: ${userId}, collection: ${collectionName}, doc: ${documentId}, status: ${status}`);
  } catch (error) {
    console.error('Error completing background job:', error);
    // Don't throw error here - this is cleanup, shouldn't fail the main operation
  }
}

/**
 * Get background job status
 * @param {string} userId - User ID
 * @param {string} collectionName - Collection name from COLLECTIONS
 * @param {string} documentId - Document ID
 * @return {Promise<Object|null>} Job data or null if not found
 */
async function getBackgroundJobStatus(userId, collectionName, documentId) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    const jobRef = db
      .collection('users')
      .doc(userId)
      .collection(collectionName)
      .doc(documentId);

    const jobDoc = await jobRef.get();
    return jobDoc.exists ? jobDoc.data() : null;
  } catch (error) {
    console.error('Error getting background job status:', error);
    return null;
  }
}

/**
 * Validate days generating constraints
 * Only allow generating if remaining 2 days for coming ones
 * @param {string} userId - User ID
 * @return {Promise<Object>} Validation result
 */
async function validateDaysGenerating(userId) {
  try {
    const admin = initializeFirebase();
    const currentServerTime = new Date();
    currentServerTime.setHours(0, 0, 0, 0); // Set to start of day for comparison

    // Query for existing meal documents
    const mealsSnapshot = await admin.firestore()
      .collection('users')
      .doc(userId)
      .collection('days')
      .orderBy('date', 'desc')
      .limit(1)
      .get();

    if (mealsSnapshot.empty) {
      // No existing meals, validation passes
      return {isValid: true};
    }

    // Get the last meal date
    const lastMealDoc = mealsSnapshot.docs[0];
    const lastMealDate = lastMealDoc.data().date.toDate();
    lastMealDate.setHours(0, 0, 0, 0); // Set to start of day for comparison

    // Calculate the difference in days between last meal date and current server time
    const timeDifferenceMs = lastMealDate.getTime() - currentServerTime.getTime();
    const daysDifference = Math.ceil(timeDifferenceMs / (1000 * 60 * 60 * 24));

    console.log('Days generating validation check:', {
      lastMealDate: lastMealDate.toISOString(),
      currentServerTime: currentServerTime.toISOString(),
      daysDifference: daysDifference,
    });

    // If the last day's date is greater than or equal to current server time by 2 days, prevent generation
    if (daysDifference >= 2) {
      const errorMessage = 'لا يمكن إنشاء خطة وجبات جديدة. لديك وجبات مجدولة لأكثر من يومين في المستقبل.';
      return {
        isValid: false,
        errorMessage: errorMessage,
        lastMealDate: lastMealDate,
        daysDifference: daysDifference,
      };
    }

    // Validation passes
    return {isValid: true};
  } catch (error) {
    console.error('Error validating days generating:', error);
    // On error, allow generation to proceed (fail-safe approach)
    return {isValid: true};
  }
}

/**
 * Validate meal image generating constraints
 * Check if max 3 attempts per meal have been reached
 * @param {string} userId - User ID
 * @param {string} mealId - Meal ID
 * @return {Promise<Object>} Validation result
 */
async function validateMealImageGenerating(userId, mealId) {
  try {
    const jobStatus = await getBackgroundJobStatus(userId, COLLECTIONS.MEAL_IMAGE_GENERATING, mealId);

    if (!jobStatus) {
      // No existing job, validation passes
      return {isValid: true};
    }

    const triggerCount = jobStatus.trigger_count || 0;
    if (triggerCount >= 3) {
      return {
        isValid: false,
        errorMessage: 'تم الوصول للحد الأقصى من المحاولات (3) لإنشاء صورة هذه الوجبة.',
        triggerCount: triggerCount,
      };
    }

    return {isValid: true};
  } catch (error) {
    console.error('Error validating meal image generating:', error);
    // On error, allow generation to proceed (fail-safe approach)
    return {isValid: true};
  }
}

/**
 * Validate meal replacement constraints
 * Check if max 3 attempts per meal have been reached
 * @param {string} userId - User ID
 * @param {string} mealId - Meal ID
 * @return {Promise<Object>} Validation result
 */
async function validateMealReplacement(userId, mealId) {
  try {
    const jobStatus = await getBackgroundJobStatus(userId, COLLECTIONS.MEAL_REPLACEMENT, mealId);

    if (!jobStatus) {
      // No existing job, validation passes
      return {isValid: true};
    }

    const triggerCount = jobStatus.trigger_count || 0;
    if (triggerCount >= 3) {
      return {
        isValid: false,
        errorMessage: 'تم الوصول للحد الأقصى من المحاولات (3) لاستبدال هذه الوجبة.',
        triggerCount: triggerCount,
      };
    }

    return {isValid: true};
  } catch (error) {
    console.error('Error validating meal replacement:', error);
    // On error, allow generation to proceed (fail-safe approach)
    return {isValid: true};
  }
}

module.exports = {
  COLLECTIONS,
  JOB_STATUS,
  checkAndStartBackgroundJob,
  completeBackgroundJob,
  getBackgroundJobStatus,
  validateDaysGenerating,
  validateMealImageGenerating,
  validateMealReplacement,
};
