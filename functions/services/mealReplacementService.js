const OpenAIService = require('./openaiService');
const {initializeFirebase} = require('../config/firebase');
const {createMealReplacementPrompt} = require('../utils/prompts');
const {parseMealPlan} = require('../utils/parsers');
const {
  generateReplacementId,
  validateReplacementLimits,
  createReplacementRecord,
  updateMealWithReplacement,
  validateReplacementMealStructure,
} = require('../utils/mealPlanHelpers');
const {getUserProfile} = require('./userService');
const {createNotification, NOTIFICATION_TYPES} = require('./notificationService');
const {
  COLLECTIONS,
  JOB_STATUS,
  checkAndStartBackgroundJob,
  completeBackgroundJob,
  validateMealReplacement,
} = require('./backgroundJobTrackingService');
const {Timestamp, FieldValue} = require('firebase-admin/firestore');

/**
 * Generate meal replacement using OpenAI
 * @param {Object} originalMeal - Original meal to replace
 * @param {Object} userProfile - User profile with preferences
 * @param {string} customIngredients - Optional custom ingredients
 * @returns {Object} Generated replacement meal
 */
async function generateReplacementMeal(originalMeal, userProfile, customIngredients) {
  try {
    console.log('Starting meal replacement generation...', {
      originalMealName: originalMeal.name,
      hasCustomIngredients: !!customIngredients,
    });
    const startTime = Date.now();

    // Create prompt for OpenAI
    const prompt = createMealReplacementPrompt(originalMeal, userProfile, customIngredients);
    console.log('Prompt created, length:', prompt.length);

    // Get user's preferred language (default to Arabic)
    const preferredLanguage = userProfile.preferred_language || 'ar';

    // Generate meal replacement using abstract OpenAI service
    const mealReplacementText = await OpenAIService.createMealPlan(prompt, preferredLanguage);

    const openAiTime = Date.now();
    console.log('OpenAI request completed in:', openAiTime - startTime, 'ms');

    console.log('OpenAI Response (first 500 chars):', mealReplacementText.substring(0, 500));

    // Parse and structure the meal replacement
    const mealData = parseMealPlan(mealReplacementText, userProfile, 1); // Single meal replacement

    // Extract the first meal from the parsed data (since we're generating a single replacement)
    const replacementMeal = mealData.days[0]?.meals[0];
    if (!replacementMeal) {
      throw new Error('Failed to extract replacement meal from generated data');
    }

    // Validate and structure the replacement meal
    const validatedMeal = validateReplacementMealStructure(replacementMeal, originalMeal);

    const totalTime = Date.now();
    console.log('Meal replacement generation completed in:', totalTime - startTime, 'ms');

    return validatedMeal;
  } catch (error) {
    console.error('Error in generateReplacementMeal service:', error);
    throw error;
  }
}

/**
 * Process meal replacement in background
 * @param {string} userId - User ID
 * @param {string} dayDocumentId - Day document ID
 * @param {string} mealId - Meal ID to replace
 * @param {string} customIngredients - Optional custom ingredients
 */
async function processReplacementInBackground(userId, dayDocumentId, mealId, customIngredients) {
  try {
    console.log('Processing meal replacement in background for user:', userId, {
      dayDocumentId,
      mealId,
    });

    // Get the current meal data using the provided document ID
    const admin = initializeFirebase();
    const dayDocRef = admin.firestore()
      .collection('users')
      .doc(userId)
      .collection('days')
      .doc(dayDocumentId);

    const dayDoc = await dayDocRef.get();
    if (!dayDoc.exists) {
      throw new Error('Day document does not exist');
    }

    const dayData = dayDoc.data();
    const meals = dayData.meals || [];

    // Find the meal by ID instead of index
    const mealIndex = meals.findIndex((meal) => meal.id === mealId);
    if (mealIndex === -1) {
      throw new Error('Meal with specified ID not found');
    }

    const currentMeal = meals[mealIndex];

    // Validate replacement limits
    const validation = validateReplacementLimits(currentMeal);
    if (!validation.isValid) {
      const error = new Error(validation.error);
      error.code = validation.code;
      throw error;
    }

    // Validate background job constraints (max 3 attempts)
    const jobValidation = await validateMealReplacement(userId, mealId);
    if (!jobValidation.isValid) {
      const error = new Error(jobValidation.errorMessage);
      error.code = 'MEAL_REPLACEMENT_BLOCKED';
      error.details = {
        triggerCount: jobValidation.triggerCount,
        reason: 'Maximum attempts exceeded',
      };
      throw error;
    }

    // Check and start background job for meal replacement
    await checkAndStartBackgroundJob(
      userId,
      COLLECTIONS.MEAL_REPLACEMENT,
      mealId,
      {}, // no meta data needed for meal replacement
      3, // max 3 attempts
      'استبدال الوجبة',
    );

    // Get user profile for preferences
    const userProfile = await getUserProfile(userId);
    if (!userProfile) {
      throw new Error('User profile not found');
    }

    // Generate replacement meal
    const replacementMeal = await generateReplacementMeal(currentMeal, userProfile, customIngredients);

    // Create replacement record
    const replacementId = generateReplacementId(userId, dayDocumentId, mealId);
    const replacement = createReplacementRecord(
      replacementId,
      replacementMeal,
      customIngredients ? 'custom_request' : 'user_request',
    );

    // Update meal with replacement
    const updatedMeal = updateMealWithReplacement(currentMeal, replacement);

    console.log('Updated meal structure:', {
      name: updatedMeal.name,
      replacementHistoryLength: updatedMeal.replacement_history.length,
      replacementCount: updatedMeal.replacement_count,
      hasOriginalInHistory: updatedMeal.replacement_history.some((r) => r.is_original),
    });

    // Update the day document
    const updatedMeals = [...meals];
    updatedMeals[mealIndex] = updatedMeal;

    await dayDocRef.update({
      meals: updatedMeals,
      last_modified: FieldValue.serverTimestamp(),
    });

    // Update user metadata timestamp
    await admin.firestore()
      .collection('users')
      .doc(userId)
      .set({
        meal_plan: {
          last_modified: FieldValue.serverTimestamp(),
        },
      }, {merge: true});

    console.log('Meal replacement completed successfully for user:', userId);

    // Create notification for meal replacement completion
    await createMealReplacementNotification(userId, replacementMeal.name, dayDocumentId, mealId);

    // Complete background job with success
    await completeBackgroundJob(userId, COLLECTIONS.MEAL_REPLACEMENT, mealId, JOB_STATUS.SUCCESS);
  } catch (error) {
    console.error('Error processing meal replacement in background:', error);

    // Complete background job with failure
    const errorMessage = error.message || 'حدث خطأ أثناء استبدال الوجبة';
    await completeBackgroundJob(userId, COLLECTIONS.MEAL_REPLACEMENT, mealId, JOB_STATUS.FAIL, errorMessage);

    throw error;
  }
}


/**
 * Create notification for meal replacement completion using notification service
 * @param {string} userId - User ID
 * @param {string} replacementMealName - Name of replacement meal
 * @param {string} dayDocumentId - Day document ID
 * @param {string} mealId - Meal ID
 */
async function createMealReplacementNotification(userId, replacementMealName, dayDocumentId, mealId) {
  try {
    console.log('Creating meal replacement notification for user:', userId);

    // Create notification using the generic notification service
    await createNotification(userId, {
      type: NOTIFICATION_TYPES.MEAL_REPLACEMENT_READY,
      content: {
        mealName: replacementMealName,
      },
      action: {
        type: 'internalNavigation',
        route: '/meal-details',
        parameters: {
          dayDocumentId: dayDocumentId,
          mealId: mealId,
          openReplacementModal: true,
        },
      },
      customId: 'meal_replacement',
    });

    console.log('Meal replacement notification created successfully for user:', userId);
  } catch (error) {
    console.error('Error creating meal replacement notification:', error);
    // Don't throw error to avoid breaking the main meal replacement flow
  }
}

module.exports = {
  processReplacementInBackground,
};
