const {getOpenAI, OPENAI_CONFIGS} = require('../config/openai');

/**
 * Abstract OpenAI service for making AI requests with standardized configurations
 */
class OpenAIService {
  /**
   * Make a chat completion request to OpenAI
   * @param {Object} options - Request options
   * @param {string} options.configType - Configuration type from OPENAI_CONFIGS
   * @param {string} options.userPrompt - User prompt content
   * @param {string} options.systemPrompt - System prompt content
   * @param {string} options.preferredLanguage - User's preferred language (optional)
   * @param {Object} options.overrides - Override default config values (optional)
   * @return {string} OpenAI response content
   */
  static async createChatCompletion({
    configType,
    userPrompt,
    systemPrompt,
    preferredLanguage = 'ar',
    overrides = {}
  }) {
    try {
      // Get OpenAI instance
      const openai = getOpenAI();
      
      // Get configuration for the specified type
      const config = OPENAI_CONFIGS[configType];
      if (!config) {
        throw new Error(`Unknown OpenAI config type: ${configType}`);
      }
      
      // Merge config with overrides
      const finalConfig = {
        ...config,
        ...overrides,
      };
      
      // Generate system message based on language and context
      const finalSystemPrompt = this.generateSystemPrompt(systemPrompt, preferredLanguage, configType);
      
      console.log(`OpenAI request - Config: ${configType}, Model: ${finalConfig.model}, Language: ${preferredLanguage}`);
      
      // Make the request
      const completion = await openai.chat.completions.create({
        model: finalConfig.model,
        messages: [
          {
            role: "system",
            content: finalSystemPrompt
          },
          {
            role: "user",
            content: userPrompt
          }
        ],
        temperature: finalConfig.temperature,
        max_tokens: finalConfig.max_tokens,
      });

      const responseContent = completion.choices[0]?.message?.content;
      if (!responseContent) {
        throw new Error('No content received from OpenAI');
      }

      console.log(`OpenAI response received - Length: ${responseContent.length} characters`);
      return responseContent;

    } catch (error) {
      console.error('Error in OpenAI chat completion:', error);
      
      // Add context to the error
      if (error.code) {
        error.context = { configType, preferredLanguage };
      }
      
      throw error;
    }
  }

  /**
   * Generate system prompt based on language and context
   * @param {string} baseSystemPrompt - Base system prompt
   * @param {string} preferredLanguage - User's preferred language
   * @param {string} configType - Configuration type for context
   * @return {string} Enhanced system prompt
   */
  static generateSystemPrompt(baseSystemPrompt, preferredLanguage, configType) {
    const isArabic = preferredLanguage === 'ar';
    
    // Language-specific base instructions
    const languageInstruction = isArabic
      ? "تجيب باللغة العربية وتقدم معلومات دقيقة ومفيدة."
      : "You respond in English and provide accurate and helpful information.";
    
    // Context-specific instructions
    const contextInstructions = this.getContextInstructions(configType, isArabic);
    
    // Combine all instructions
    return `${baseSystemPrompt} ${languageInstruction} ${contextInstructions}`.trim();
  }

  /**
   * Get context-specific instructions based on config type
   * @param {string} configType - Configuration type
   * @param {boolean} isArabic - Whether to use Arabic language
   * @return {string} Context-specific instructions
   */
  static getContextInstructions(configType, isArabic) {
    const instructions = {
      MEAL_PLAN: {
        ar: "يجب أن تكون الاستجابة بتنسيق JSON صالح فقط. لا تضع أي نص قبل أو بعد JSON.",
        en: "You MUST respond with valid JSON only. Do not include any text before or after the JSON."
      },
      SHOPPING_LIST: {
        ar: "قم بإنشاء قائمة تسوق منظمة بتنسيق JSON مع فئات واضحة.",
        en: "Create an organized shopping list in JSON format with clear categories."
      },
      NUTRITION_ANALYSIS: {
        ar: "قدم تحليل غذائي دقيق ومفصل بتنسيق JSON.",
        en: "Provide accurate and detailed nutritional analysis in JSON format."
      },
      MEAL_REPLACEMENT: {
        ar: "اقترح بدائل وجبات صحية ومناسبة بتنسيق JSON.",
        en: "Suggest healthy and suitable meal alternatives in JSON format."
      }
    };

    const configInstructions = instructions[configType];
    if (!configInstructions) {
      return isArabic ? "قدم استجابة مفيدة ودقيقة." : "Provide a helpful and accurate response.";
    }

    return isArabic ? configInstructions.ar : configInstructions.en;
  }

  /**
   * Create a meal plan using OpenAI
   * @param {string} prompt - Meal plan prompt
   * @param {string} preferredLanguage - User's preferred language
   * @param {Object} overrides - Config overrides
   * @return {string} OpenAI response
   */
  static async createMealPlan(prompt, preferredLanguage = 'ar', overrides = {}) {
    const systemPrompt = "أنت خبير تغذية ومخطط وجبات محترف.";
    
    return this.createChatCompletion({
      configType: 'MEAL_PLAN',
      userPrompt: prompt,
      systemPrompt,
      preferredLanguage,
      overrides,
    });
  }

  /**
   * Create a shopping list using OpenAI
   * @param {string} prompt - Shopping list prompt
   * @param {string} preferredLanguage - User's preferred language
   * @param {Object} overrides - Config overrides
   * @return {string} OpenAI response
   */
  static async createShoppingList(prompt, preferredLanguage = 'ar', overrides = {}) {
    const systemPrompt = "أنت مساعد ذكي متخصص في التغذية وإنشاء قوائم التسوق.";
    
    return this.createChatCompletion({
      configType: 'SHOPPING_LIST',
      userPrompt: prompt,
      systemPrompt,
      preferredLanguage,
      overrides,
    });
  }

  /**
   * Analyze nutrition using OpenAI
   * @param {string} prompt - Nutrition analysis prompt
   * @param {string} preferredLanguage - User's preferred language
   * @param {Object} overrides - Config overrides
   * @return {string} OpenAI response
   */
  static async analyzeNutrition(prompt, preferredLanguage = 'ar', overrides = {}) {
    const systemPrompt = "أنت خبير تغذية متخصص في تحليل القيم الغذائية.";
    
    return this.createChatCompletion({
      configType: 'NUTRITION_ANALYSIS',
      userPrompt: prompt,
      systemPrompt,
      preferredLanguage,
      overrides,
    });
  }

  /**
   * Generate meal replacement suggestions using OpenAI
   * @param {string} prompt - Meal replacement prompt
   * @param {string} preferredLanguage - User's preferred language
   * @param {Object} overrides - Config overrides
   * @return {string} OpenAI response
   */
  static async generateMealReplacement(prompt, preferredLanguage = 'ar', overrides = {}) {
    const systemPrompt = "أنت خبير تغذية متخصص في اقتراح بدائل الوجبات الصحية.";

    return this.createChatCompletion({
      configType: 'MEAL_REPLACEMENT',
      userPrompt: prompt,
      systemPrompt,
      preferredLanguage,
      overrides,
    });
  }

  /**
   * Generate meal image using OpenAI DALL-E
   * @param {string} prompt - Image generation prompt
   * @param {string} preferredLanguage - User's preferred language (not used for images but kept for consistency)
   * @param {Object} overrides - Config overrides
   * @return {string} Generated image URL
   */
  static async createMealImage(prompt, preferredLanguage = 'ar', overrides = {}) {
    try {
      console.log('OpenAI DALL-E request - Generating meal image');
      console.log('Image prompt length:', prompt.length);

      // Get OpenAI instance
      const openai = getOpenAI();

      // Default image generation config
      const defaultConfig = {
        model: "dall-e-3",
        n: 1,
        size: "1024x1024",
        quality: "standard",
        style: "natural"
      };

      // Apply overrides
      const finalConfig = { ...defaultConfig, ...overrides };

      console.log(`DALL-E request - Model: ${finalConfig.model}, Size: ${finalConfig.size}, Quality: ${finalConfig.quality}`);

      // Make the image generation request
      const response = await openai.images.generate({
        model: finalConfig.model,
        prompt: prompt,
        n: finalConfig.n,
        size: finalConfig.size,
        quality: finalConfig.quality,
        style: finalConfig.style
      });

      const imageUrl = response.data[0]?.url;
      if (!imageUrl) {
        throw new Error('No image URL received from OpenAI DALL-E');
      }

      console.log('DALL-E response received - Image generated successfully');
      return imageUrl;

    } catch (error) {
      console.error('Error in OpenAI image generation:', error);

      // Add context to the error
      if (error.code) {
        error.context = { preferredLanguage, promptLength: prompt.length };
      }

      throw error;
    }
  }
}

module.exports = OpenAIService;
