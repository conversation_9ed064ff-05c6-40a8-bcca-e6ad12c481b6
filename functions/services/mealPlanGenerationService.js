const OpenAIService = require('./openaiService');
const {initializeFirebase} = require('../config/firebase');
const {createMealPlanPrompt} = require('../utils/prompts');
const {parseMealPlan} = require('../utils/parsers');
const {
  generateMealId,
  formatDateRangeForNotification,
  getMealPlanNotificationContent,
} = require('../utils/mealPlanHelpers');
const {getUserProfile} = require('./userService');
const {createNotification, formatDateRange, NOTIFICATION_TYPES} = require('./notificationService');
const {checkAndSetBackgroundJob, clearBackgroundJob} = require('../utils/firestoreHelpers');
const {Timestamp, FieldValue} = require('firebase-admin/firestore');

/**
 * Generates a meal plan using OpenAI
 * @param {Object} preferences - User dietary preferences
 * @param {number} duration - Number of days for the meal plan
 * @param {string} userId - User ID for saving the plan
 * @return {Object} Generated meal plan
 */
async function generateMealPlan(preferences, duration, _userId) {
  try {
    console.log('Starting meal plan generation...', {duration, userId: _userId});
    const startTime = Date.now();

    // Create prompt for OpenAI
    const prompt = createMealPlanPrompt(preferences, duration);
    console.log('Prompt created, length:', prompt.length);

    // Get user's preferred language (default to Arabic)
    const preferredLanguage = preferences.preferred_language || 'ar';

    // Generate meal plan using abstract OpenAI service
    const mealPlanText = await OpenAIService.createMealPlan(prompt, preferredLanguage);

    const openAiTime = Date.now();
    console.log('OpenAI request completed in:', openAiTime - startTime, 'ms');

    console.log('OpenAI Response (first 500 chars):', mealPlanText.substring(0, 500));

    // Parse and structure the meal plan
    const mealPlan = parseMealPlan(mealPlanText, preferences, duration);

    const totalTime = Date.now();
    console.log('Meal plan generation completed in:', totalTime - startTime, 'ms');

    return mealPlan;
  } catch (error) {
    console.error('Error in generateMealPlan service:', error);
    throw error;
  }
}



/**
 * Validate user profile exists and extract preferences for meal generation
 * @param {string} userId - User ID
 * @returns {Object} Validation result with preferences and duration
 */
async function validateUserProfileForMealGeneration(userId) {
  try {
    const userProfile = await getUserProfile(userId);
    
    if (!userProfile) {
      const error = new Error('User profile not found. Please complete onboarding first.');
      error.code = 'USER_NOT_FOUND';
      throw error;
    }

    // Extract preferences from plan_preferences only (not from userProfile root)
    const planPrefs = userProfile.plan_preferences || {};

    const preferences = {
      // Basic dietary information (from plan_preferences with snake_case)
      dietaryRestrictions: planPrefs.dietary_restrictions || [],
      allergies: planPrefs.allergies || [],
      cuisinePreferences: planPrefs.favorite_cuisines || [],
      favoriteIngredients: planPrefs.favorite_ingredients || [],
      dislikedIngredients: planPrefs.disliked_ingredients || [],

      // Meal structure (from plan_preferences with snake_case)
      mealsPerDay: planPrefs.meals_per_day || 3,
      snacksPerDay: planPrefs.snacks_per_day || 2,

      // Nutritional goals (from plan_preferences with snake_case)
      calorieGoal: planPrefs.daily_calorie_goal || 2000,
      proteinGoal: Math.round(planPrefs.protein_goal || 0),
      carbsGoal: Math.round(planPrefs.carbs_goal || 0),
      fatGoal: Math.round(planPrefs.fat_goal || 0),
      fiberGoal: Math.round(planPrefs.fiber_goal || 0),

      // Physical information for better meal planning (from plan_preferences with snake_case)
      height: planPrefs.height,
      weight: planPrefs.weight,
      age: planPrefs.date_of_birth ?
        Math.floor((Date.now() - new Date(planPrefs.date_of_birth).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) : null,
      gender: planPrefs.gender,
      activityLevel: planPrefs.activity_level,
      useManualCalories: planPrefs.use_manual_calories,
      dailyBurnedCalories: planPrefs.daily_burned_calories,

      // Health goals (from plan_preferences with snake_case)
      healthGoal: planPrefs.health_goal,
      targetWeight: planPrefs.target_weight,

      // Health conditions (from plan_preferences with snake_case)
      healthConditions: planPrefs.health_conditions || [],
      medications: planPrefs.medications || [],

      // System preferences (from plan_preferences with snake_case)
      unitSystem: planPrefs.unit_system || 'metric',
      preferredLanguage: userProfile.preferred_language || 'ar', // This comes from user profile root

      dietType: 'normal', // Default diet type, can be enhanced later
    };

    // Fixed duration of 3 days as specified in requirements
    const duration = 3;

    return {
      isValid: true,
      preferences,
      duration,
    };
  } catch (error) {
    console.error('Error validating user profile:', error);
    throw error;
  }
}

/**
 * Generate and save complete meal plan with notifications
 * @param {string} userId - User ID
 * @param {Object} preferences - User preferences
 * @param {number} duration - Duration in days
 */
async function generateAndSaveMealPlan(userId, preferences, duration) {
  try {
    console.log('Service: Generating and saving meal plan for user:', userId);

    // Check and set background job to prevent concurrent meal plan generation
    await checkAndSetBackgroundJob(
      userId,
      'meal_plan_generation',
      'MEAL_PLAN_GENERATION',
      5, // 5 minutes timeout
      'يتم حالياً إنشاء خطة وجبات أخرى. يرجى الانتظار حتى اكتمالها.'
    );

    try {
      // Generate meal plan
      const mealPlan = await generateMealPlan(preferences, duration, userId);

      // Find the last existing meal date to determine starting date for new meals
      const startDate = await findNextMealDate(userId);
      console.log('Service: Starting new meals from date:', startDate);

      // Assign sequential dates to the generated days and unique hash-based IDs to meals
      const datedDays = mealPlan.days.map((day, index) => {
        const dayDate = new Date(startDate);
        dayDate.setDate(dayDate.getDate() + index);

        // Generate unique hash-based IDs for each meal in the day
        const mealsWithIds = day.meals.map((meal, mealIndex) => {
          return {
            ...meal,
            id: generateMealId(userId, dayDate, mealIndex, meal.name),
          };
        });

        return {
          ...day,
          date: dayDate,
          meals: mealsWithIds,
        };
      });

      // Save each day as a separate document in Firestore
      const admin = initializeFirebase();
      const batch = admin.firestore().batch();

      for (const day of datedDays) {
        const dayDocRef = admin.firestore()
          .collection('users')
          .doc(userId)
          .collection('days')
          .doc(); // Auto-generate document ID

        const dayData = {
          date: Timestamp.fromDate(day.date),
          meals: day.meals,
          total_nutrition: day.total_nutrition || day.totalNutrition, // Support both snake_case and camelCase for backward compatibility
          created_at: FieldValue.serverTimestamp(),
          last_modified: FieldValue.serverTimestamp(),
        };

        batch.set(dayDocRef, dayData);
      }

      // Commit the batch
      await batch.commit();

      // Mark onboarding as completed since meal plan generation is successful
      await admin.firestore()
        .collection('users')
        .doc(userId)
        .set({
          onboarding_completed: true,
          updated_at: FieldValue.serverTimestamp(),
        }, {merge: true});

      console.log('Service: Successfully saved', datedDays.length, 'days to Firestore for user:', userId);

      // Add meal generation tracker
      const generationId = `meal_gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      await addMealGenerationTracker(userId, generationId, datedDays.length);

      // Create notification for meal plan generation completion using notification service
      await createMealPlanGenerationNotification(userId, datedDays.length, datedDays[0].date);

      // Clear background job tracker
      await clearBackgroundJob(userId, 'meal_plan_generation');

    } catch (error) {
      // Clear background job tracker on error
      await clearBackgroundJob(userId, 'meal_plan_generation');
      throw error;
    }

  } catch (error) {
    console.error('Service: Error generating and saving meal plan for user:', userId, error);
    throw error;
  }
}

/**
 * Find the next available date for new meals
 * Returns the day after the last existing meal, or today if no meals exist or if latest date is in the past
 * @param {string} userId - User ID
 * @returns {Date} Next meal date
 */
async function findNextMealDate(userId) {
  try {
    const admin = initializeFirebase();
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to start of day for comparison

    // Query for the most recent meal document
    const mealsSnapshot = await admin.firestore()
      .collection('users')
      .doc(userId)
      .collection('days')
      .orderBy('date', 'desc')
      .limit(1)
      .get();

    if (mealsSnapshot.empty) {
      // No existing meals, start from today
      return today;
    }

    // Get the last meal date
    const lastMealDoc = mealsSnapshot.docs[0];
    const lastMealDate = lastMealDoc.data().date.toDate();
    lastMealDate.setHours(0, 0, 0, 0); // Set to start of day for comparison

    // If the latest meal date is less than current server time, return today
    if (lastMealDate < today) {
      console.log('Latest meal date is in the past, starting from today');
      return today;
    }

    // Otherwise, return the day after the last meal
    const nextDate = new Date(lastMealDate);
    nextDate.setDate(nextDate.getDate() + 1);

    return nextDate;
  } catch (error) {
    console.error('Error finding next meal date:', error);
    // Fallback to today's date
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return today;
  }
}

/**
 * Create notification for meal plan generation completion using notification service
 * @param {string} userId - User ID
 * @param {number} daysCount - Number of days in meal plan
 * @param {Date} firstDayDate - First day date
 */
async function createMealPlanGenerationNotification(userId, daysCount, firstDayDate) {
  try {
    console.log('Creating meal plan generation notification for user:', userId);

    // Get user profile to determine preferred language for date formatting
    const admin = initializeFirebase();
    const db = admin.firestore();
    const userDoc = await db.collection('users').doc(userId).get();
    const userData = userDoc.data();
    const preferredLanguage = userData?.preferred_language || 'ar';

    // Format the date range for notification message
    const dateRange = formatDateRange(firstDayDate, null, preferredLanguage);

    // Create notification using the generic notification service
    await createNotification(userId, {
      type: NOTIFICATION_TYPES.MEAL_PLAN_READY,
      content: {
        duration: daysCount,
        planType: 'standard', // Could be enhanced later with different plan types
        dateRange: dateRange,
      },
      action: {
        type: 'internalNavigation',
        route: '/home',
        parameters: {
          focusFirstDay: true,
          firstDayDate: firstDayDate.toISOString().split('T')[0], // YYYY-MM-DD format
        },
      },
      customId: 'meal_plan',
    });

    console.log('Meal plan generation notification created successfully for user:', userId);
  } catch (error) {
    console.error('Error creating meal plan generation notification:', error);
    // Don't throw error to avoid breaking the main meal plan generation flow
  }
}

/**
 * Add meal generation tracker indicator to subcollection
 * @param {string} userId - User ID
 * @param {string} generationId - Generation ID
 * @param {number} daysCount - Number of days generated
 */
async function addMealGenerationTracker(userId, generationId, daysCount) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    const trackerDocRef = db.collection('users')
      .doc(userId)
      .collection('meal_generation_trackers')
      .doc(generationId);

    const trackerData = {
      datetime: Timestamp.now(),
      id: generationId,
      days_count: daysCount,
      status: 'completed',
    };

    await trackerDocRef.set(trackerData);
    console.log('Meal generation tracker created for user:', userId, 'generationId:', generationId, 'daysCount:', daysCount);
  } catch (error) {
    console.error('Error adding meal generation tracker:', error);
    throw error;
  }
}

module.exports = {
  generateMealPlan,
  validateUserProfileForMealGeneration,
  generateAndSaveMealPlan,
  findNextMealDate,
  createMealPlanGenerationNotification,
  addMealGenerationTracker,
};
